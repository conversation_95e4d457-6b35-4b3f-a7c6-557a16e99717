/* Unified Threshold Sliders - Clean, properly scaled system */
.unified-threshold-container {
  position: relative !important;
  width: 100% !important;
  margin: 20px 0 !important;
  padding: 15px !important;
  background: rgba(0, 20, 40, 0.8) !important;
  border: 1px solid rgba(0, 255, 255, 0.3) !important;
  border-radius: 8px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3) !important;
  box-sizing: border-box !important;
}

.unified-slider-label {
  color: #00FFFF !important;
  font-size: 14px !important;
  font-weight: bold !important;
  text-align: center !important;
  margin-bottom: 15px !important;
  text-shadow: 0 0 5px rgba(0, 255, 255, 0.5) !important;
}

.unified-slider-track {
  position: relative !important;
  width: 100% !important;
  height: 20px !important;
  background: #333 !important;
  border-radius: 10px !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  overflow: visible !important;
  margin: 10px 0 !important;
  box-sizing: border-box !important;
}

.unified-thumb {
  position: absolute !important;
  top: -8px !important;
  width: 16px !important;
  height: 36px !important;
  border: 2px solid #FFFFFF !important;
  border-radius: 8px !important;
  cursor: grab !important;
  transform: translateX(-50%) !important;
  box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4) !important;
  transition: all 0.2s ease !important;
  z-index: 110 !important;
}

.unified-thumb:hover {
  transform: translateX(-50%) scale(1.1) !important;
  z-index: 120 !important;
}

.unified-thumb:active {
  cursor: grabbing !important;
  transform: translateX(-50%) scale(1.2) !important;
  z-index: 130 !important;
}

.unified-value-display {
  display: flex !important;
  justify-content: space-between !important;
  margin-top: 15px !important;
  font-size: 11px !important;
  font-family: 'Roboto Mono', monospace !important;
}

.unified-value-display span {
  font-weight: bold !important;
  text-shadow: 0 0 3px currentColor !important;
}

.unified-reset-button {
  display: block !important;
  margin: 20px auto !important;
  padding: 10px 20px !important;
  background: linear-gradient(135deg, rgba(0, 200, 255, 0.8), rgba(0, 100, 200, 0.8)) !important;
  color: white !important;
  border: none !important;
  border-radius: 6px !important;
  cursor: pointer !important;
  font-weight: bold !important;
  font-size: 12px !important;
  transition: all 0.3s ease !important;
  border: 1px solid rgba(0, 255, 255, 0.3) !important;
}

.unified-reset-button:hover {
  background: linear-gradient(135deg, rgba(0, 255, 255, 0.9), rgba(0, 150, 255, 0.9)) !important;
  box-shadow: 0 0 15px rgba(0, 255, 255, 0.7) !important;
  transform: translateY(-2px) scale(1.05) !important;
}

/* Hide old threshold slider implementations to prevent conflicts */
.threshold-group:not(.unified-threshold-container),
.slider-group:not(.unified-threshold-container),
.threshold-container:not(.unified-threshold-container) {
  display: none !important;
}

/* Ensure the unified sliders are always visible and properly scaled */
#threshold-sliders .unified-threshold-container {
  display: block !important;
  visibility: visible !important;
  opacity: 1 !important;
}

/* Fix any scaling issues */
#thresholdsMenu .unified-threshold-container {
  width: 100% !important;
  max-width: 100% !important;
  min-width: 300px !important;
  transform: none !important;
  scale: 1 !important;
}

/* Ensure proper positioning in menu */
#thresholdsMenu {
  overflow-y: auto !important;
  max-height: 80vh !important;
}

#thresholdsMenu .menu-content {
  padding: 20px !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .unified-threshold-container {
    margin: 15px 0 !important;
    padding: 12px !important;
  }
  
  .unified-slider-label {
    font-size: 12px !important;
  }
  
  .unified-thumb {
    width: 14px !important;
    height: 32px !important;
  }
  
  .unified-value-display {
    font-size: 10px !important;
    flex-wrap: wrap !important;
    gap: 5px !important;
  }
}
