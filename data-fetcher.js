const fetch = require('node-fetch').default
const { TIMEFRAMES, DEFAULT_PAIR, DEFAULT_TIMEFRAME } = require('./config')

// Data storage
const historicalData = {}
const indicatorsData = {}
const lastLivePrice = {}

// Initialize live data for a trading pair
function initializeLiveData(pair) {
  if (!historicalData[pair]) {
    historicalData[pair] = {}
    indicatorsData[pair] = {}

    // Initialize data for each timeframe
    TIMEFRAMES.forEach(tf => {
      historicalData[pair][tf] = []
      indicatorsData[pair][tf] = {}
    })

    console.log(`Initialized data for pair: ${pair}`)
  }
}

// Fetch live price for a trading pair
async function fetchLivePrice(pair) {
  try {
    const response = await fetch(`https://api.kraken.com/0/public/Ticker?pair=${pair}`)
    const data = await response.json()

    if (data.error && data.error.length > 0) {
      throw new Error(data.error.join(', '))
    }

    const tickerData = data.result[pair] || data.result[`X${pair}`] || data.result[`X${pair}Z`]
    if (!tickerData) {
      throw new Error('No ticker data found')
    }

    const price = {
      price: parseFloat(tickerData.c[0]),
      volume: parseFloat(tickerData.v[1]),
      timestamp: Date.now(),
    }

    lastLivePrice[pair] = price
    return price
  } catch (error) {
    console.error(`❌ TRADING INTEGRITY: Error fetching live price for ${pair}:`, error.message)

    // NO DUMMY DATA - Return null if no real data available
    if (lastLivePrice[pair]) {
      console.warn(`Using last known real price for ${pair}`);
      return lastLivePrice[pair];
    }

    console.error(`❌ NO REAL DATA: No price data available for ${pair}`);
    return null; // NO DUMMY DATA
  }
}

// Fetch OHLC data for a trading pair and timeframe
async function fetchOHLCData(pair, timeframe) {
  try {
    const interval = getIntervalFromTimeframe(timeframe)
    const response = await fetch(
      `https://api.kraken.com/0/public/OHLC?pair=${pair}&interval=${interval}`,
    )

    const data = await response.json()

    if (data.error && data.error.length > 0) {
      throw new Error(data.error.join(', '))
    }

    const ohlcData = data.result[pair] || data.result[`X${pair}`] || data.result[`X${pair}Z`] || []

    // Process and store the data
    const processedData = ohlcData.map(item => ({
      time: parseInt(item[0]),
      open: parseFloat(item[1]),
      high: parseFloat(item[2]),
      low: parseFloat(item[3]),
      close: parseFloat(item[4]),
      vwap: parseFloat(item[5]),
      volume: parseFloat(item[6]),
      count: parseInt(item[7]),
    }))

    // Update historical data
    if (!historicalData[pair]) {
      historicalData[pair] = {}
    }
    historicalData[pair][timeframe] = processedData

    return processedData
  } catch (error) {
    console.error(`Error fetching OHLC data for ${pair}/${timeframe}:`, error.message)
    return []
  }
}

// Helper function to get interval from timeframe
function getIntervalFromTimeframe(timeframe) {
  const intervals = {
    '1m': 1,
    '5m': 5,
    '15m': 15,
    '1h': 60,
    '4h': 240,
    '1d': 1440,
    '1w': 10080,
  }

  return intervals[timeframe] || 60 // Default to 1h if timeframe is invalid
}

module.exports = {
  initializeLiveData,
  fetchLivePrice,
  fetchOHLCData,
  historicalData,
  indicatorsData,
  lastLivePrice,
}
