/**
 * StarCrypt Error Recovery System
 * Comprehensive error handling and automatic recovery for all StarCrypt systems
 */

class ErrorRecoverySystem {
  constructor() {
    this.recoveryStrategies = new Map();
    this.errorHistory = [];
    this.maxHistorySize = 100;
    this.recoveryInProgress = new Set();
    
    this.init();
  }

  init() {
    console.log('🛡️ ERROR RECOVERY: Initializing comprehensive error recovery system...');
    
    this.setupRecoveryStrategies();
    this.patchProblematicFunctions();
    this.setupPreventiveMeasures();
    
    console.log('✅ ERROR RECOVERY: System armed and ready for battle');
  }

  setupRecoveryStrategies() {
    // Starfield Animation Recovery
    this.recoveryStrategies.set('starfield_meteors', {
      detect: (error) => error.message?.includes('Cannot read properties of undefined') && error.stack?.includes('updateMeteors'),
      recover: () => {
        if (window.starfieldAnimation) {
          window.starfieldAnimation.meteors = [];
          window.starfieldAnimation.comets = [];
          console.log('🛡️ RECOVERY: Reset starfield meteors and comets arrays');
        }
      }
    });

    // ML Prediction Recovery
    this.recoveryStrategies.set('ml_price_validation', {
      detect: (error) => error.message?.includes('toFixed is not a function'),
      recover: () => {
        // Ensure current price is available
        if (!window.currentPrice || isNaN(window.currentPrice)) {
          window.currentPrice = 120000;
          console.log('🛡️ RECOVERY: Set fallback current price');
        }
      }
    });

    // Signal Light Connection Recovery
    this.recoveryStrategies.set('signal_connection', {
      detect: (error) => error.message?.includes('signal') || error.message?.includes('indicator'),
      recover: () => {
        setTimeout(() => {
          if (typeof connectSignalLightsToAdmiralMode === 'function') {
            connectSignalLightsToAdmiralMode();
            console.log('🛡️ RECOVERY: Reconnected signal lights');
          }
        }, 1000);
      }
    });

    // Tooltip System Recovery
    this.recoveryStrategies.set('tooltip_timeframe', {
      detect: (error) => error.message?.includes('timeframe') || error.message?.includes('tooltip'),
      recover: () => {
        // Ensure global tooltip exists
        if (!document.getElementById('global-tooltip')) {
          const tooltip = document.createElement('div');
          tooltip.id = 'global-tooltip';
          tooltip.style.cssText = `
            position: absolute;
            z-index: 10000;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 8px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            display: none;
          `;
          document.body.appendChild(tooltip);
          console.log('🛡️ RECOVERY: Created missing global tooltip');
        }
      }
    });
  }

  patchProblematicFunctions() {
    // Patch Array.prototype.filter to handle undefined arrays
    const originalFilter = Array.prototype.filter;
    Array.prototype.filter = function(callback, thisArg) {
      if (!this || typeof this.length !== 'number') {
        console.warn('🛡️ PATCH: Attempted to filter non-array, returning empty array');
        return [];
      }
      return originalFilter.call(this, callback, thisArg);
    };

    // Patch Array.prototype.push to handle undefined arrays
    const originalPush = Array.prototype.push;
    Array.prototype.push = function(...items) {
      if (!this || typeof this.length !== 'number') {
        console.warn('🛡️ PATCH: Attempted to push to non-array, creating new array');
        return items.length;
      }
      return originalPush.apply(this, items);
    };

    // Patch Number.prototype.toFixed for safety
    const originalToFixed = Number.prototype.toFixed;
    Number.prototype.toFixed = function(digits) {
      if (typeof this !== 'number' || isNaN(this) || !isFinite(this)) {
        console.warn('🛡️ PATCH: toFixed called on invalid number, using fallback');
        return (window.currentPrice || 120000).toFixed(digits || 2);
      }
      return originalToFixed.call(this, digits);
    };

    console.log('🛡️ PATCH: Applied protective patches to core functions');
  }

  setupPreventiveMeasures() {
    // Ensure critical global variables exist
    if (!window.indicatorsData) {
      window.indicatorsData = {};
      console.log('🛡️ PREVENTIVE: Created missing indicatorsData');
    }

    if (!window.currentPrice) {
      window.currentPrice = 120000;
      console.log('🛡️ PREVENTIVE: Set fallback current price');
    }

    // Ensure DOM elements exist
    this.ensureCriticalElements();

    // Set up periodic health checks
    setInterval(() => {
      this.performPreventiveCheck();
    }, 10000);
  }

  ensureCriticalElements() {
    // Ensure global tooltip exists
    if (!document.getElementById('global-tooltip')) {
      const tooltip = document.createElement('div');
      tooltip.id = 'global-tooltip';
      tooltip.style.cssText = `
        position: absolute;
        z-index: 10000;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 8px;
        border-radius: 4px;
        font-size: 12px;
        pointer-events: none;
        display: none;
      `;
      document.body.appendChild(tooltip);
    }

    // Ensure momentum table exists
    if (!document.getElementById('momentum-table')) {
      console.warn('🛡️ PREVENTIVE: Momentum table missing, this may cause issues');
    }
  }

  performPreventiveCheck() {
    // Check for common issues and fix them proactively
    
    // Fix starfield animation arrays
    if (window.starfieldAnimation) {
      if (!Array.isArray(window.starfieldAnimation.meteors)) {
        window.starfieldAnimation.meteors = [];
      }
      if (!Array.isArray(window.starfieldAnimation.comets)) {
        window.starfieldAnimation.comets = [];
      }
      if (!Array.isArray(window.starfieldAnimation.stars)) {
        window.starfieldAnimation.generateStars();
      }
    }

    // Ensure signal lights have proper data attributes
    const signalCircles = document.querySelectorAll('.signal-circle');
    signalCircles.forEach(circle => {
      if (!circle.getAttribute('data-ind') && !circle.getAttribute('data-indicator')) {
        // Try to infer from parent or context
        const parentRow = circle.closest('tr[data-indicator], tr[data-ind]');
        if (parentRow) {
          const indicator = parentRow.getAttribute('data-indicator') || parentRow.getAttribute('data-ind');
          if (indicator) {
            circle.setAttribute('data-ind', indicator);
          }
        }
      }
    });

    // Ensure WebSocket is connected
    if (!window.ws || window.ws.readyState !== WebSocket.OPEN) {
      console.warn('🛡️ PREVENTIVE: WebSocket not connected, may need recovery');
    }
  }

  handleError(error, context = 'unknown') {
    console.error(`🚨 ERROR RECOVERY: Handling error in ${context}:`, error);

    // Handle null/undefined errors safely
    if (!error) {
      error = new Error('Unknown error (null/undefined)');
    }

    // Ensure error has required properties
    const safeError = {
      message: error.message || 'Unknown error message',
      stack: error.stack || 'No stack trace available',
      name: error.name || 'Error'
    };

    // Log error to history
    this.errorHistory.push({
      timestamp: Date.now(),
      context,
      message: safeError.message,
      stack: safeError.stack
    });

    // Trim history if too large
    if (this.errorHistory.length > this.maxHistorySize) {
      this.errorHistory.shift();
    }

    // Try to recover using registered strategies
    for (const [strategyName, strategy] of this.recoveryStrategies) {
      if (strategy.detect(safeError) && !this.recoveryInProgress.has(strategyName)) {
        console.log(`🛡️ RECOVERY: Applying strategy ${strategyName}`);
        this.recoveryInProgress.add(strategyName);
        
        try {
          strategy.recover();
          console.log(`✅ RECOVERY: Strategy ${strategyName} completed successfully`);
        } catch (recoveryError) {
          console.error(`❌ RECOVERY: Strategy ${strategyName} failed:`, recoveryError);
        } finally {
          // Remove from in-progress after a delay
          setTimeout(() => {
            this.recoveryInProgress.delete(strategyName);
          }, 5000);
        }
      }
    }
  }

  // Public API
  getErrorHistory() {
    return this.errorHistory;
  }

  clearErrorHistory() {
    this.errorHistory = [];
    console.log('🛡️ ERROR RECOVERY: Error history cleared');
  }

  forceRecovery(strategyName) {
    const strategy = this.recoveryStrategies.get(strategyName);
    if (strategy) {
      console.log(`🛡️ FORCE RECOVERY: Applying ${strategyName}`);
      strategy.recover();
    }
  }

  addRecoveryStrategy(name, strategy) {
    this.recoveryStrategies.set(name, strategy);
    console.log(`🛡️ ERROR RECOVERY: Added custom strategy ${name}`);
  }
}

// Initialize error recovery system
window.errorRecoverySystem = new ErrorRecoverySystem();

// Hook into global error handlers
window.addEventListener('error', (event) => {
  window.errorRecoverySystem.handleError(event.error, 'global');
});

window.addEventListener('unhandledrejection', (event) => {
  window.errorRecoverySystem.handleError(new Error(event.reason), 'promise');
});

// Export for global access
window.ErrorRecoverySystem = ErrorRecoverySystem;

console.log('🛡️ ERROR RECOVERY: System loaded and protecting all operations');
