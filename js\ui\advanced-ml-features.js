/**
 * Advanced ML Features for StarCrypt
 * Enhanced ML with Admiral mode integration, 5-color threshold logic, and actionable visual effects
 */

class AdvancedMLFeatures {
  constructor() {
    this.admiralMode = false;
    this.currentStrategy = 'admiral_toa';
    this.mlOptions = {
      confidenceThreshold: 0.7,
      riskLevel: 'medium',
      timeHorizon: 'short',
      signalStrength: 'normal',
      adaptiveThresholds: true
    };
    
    this.actionableSignals = [];
    this.entryPredictions = {
      long: { price: 0, confidence: 0, timeframe: '', reasoning: '' },
      short: { price: 0, confidence: 0, timeframe: '', reasoning: '' }
    };
    
    this.visualEffects = {
      pulseIntensity: 1.0,
      glowEnabled: true,
      animationSpeed: 1.0,
      convergenceHighlight: true
    };
    
    this.init();
  }

  init() {
    console.log('[AdvancedML] Initializing advanced ML features...');
    
    try {
      this.createMLOptionsPanel();
      this.setupAdmiralModeIntegration();
      this.initializeActionableSignals();
      this.setupVisualEffects();
      this.startMLAnalysisEngine();
      
      console.log('[AdvancedML] Advanced ML features initialized successfully');
    } catch (error) {
      console.error('[AdvancedML] Error initializing advanced ML features:', error);
    }
  }

  showMLOptionFeedback(option, value) {
    // Create or update feedback display
    let feedbackContainer = document.getElementById('mlOptionsFeedback');
    if (!feedbackContainer) {
      feedbackContainer = document.createElement('div');
      feedbackContainer.id = 'mlOptionsFeedback';
      feedbackContainer.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: linear-gradient(135deg, rgba(0, 255, 255, 0.9), rgba(0, 128, 255, 0.9));
        color: #000;
        padding: 10px 15px;
        border-radius: 8px;
        font-weight: bold;
        z-index: 10000;
        box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        transition: all 0.3s ease;
      `;
      document.body.appendChild(feedbackContainer);
    }

    // Show feedback message
    const optionNames = {
      confidence: 'Confidence Threshold',
      riskLevel: 'Risk Level',
      timeHorizon: 'Time Horizon',
      signalStrength: 'Signal Strength'
    };

    feedbackContainer.innerHTML = `
      🎯 ${optionNames[option] || option} updated to: <strong>${typeof value === 'number' ? (value * 100).toFixed(0) + '%' : value}</strong>
      <br><small>Regenerating predictions...</small>
    `;

    // Auto-hide after 3 seconds
    setTimeout(() => {
      if (feedbackContainer) {
        feedbackContainer.style.opacity = '0';
        setTimeout(() => {
          if (feedbackContainer && feedbackContainer.parentNode) {
            feedbackContainer.parentNode.removeChild(feedbackContainer);
          }
        }, 300);
      }
    }, 3000);
  }

  createMLOptionsPanel() {
    console.log('[AdvancedML] Creating ML options panel...');
    
    // Find or create ML container
    let mlContainer = document.getElementById('ml-advanced-options');
    if (!mlContainer) {
      mlContainer = document.createElement('div');
      mlContainer.id = 'ml-advanced-options';
      mlContainer.className = 'ml-advanced-container';
      
      // Insert after ML chart
      const mlChart = document.getElementById('mlAnalysisChart');
      if (mlChart && mlChart.parentNode) {
        mlChart.parentNode.insertBefore(mlContainer, mlChart.nextSibling);
      }
    }

    mlContainer.innerHTML = `
      <div class="ml-options-header">
        <h3>🤖 Advanced ML Control Center</h3>
        <button class="ml-toggle-button" id="toggleMLOptions">⚙️ Options</button>
      </div>
      
      <div class="ml-options-content" id="mlOptionsContent">
        <div class="ml-option-group">
          <label class="ml-option-label">
            <input type="checkbox" id="admiralModeToggle" ${this.admiralMode ? 'checked' : ''}>
            <span class="ml-checkbox-custom"></span>
            Admiral Mode (5-Color Threshold Logic)
          </label>
        </div>
        
        <div class="ml-option-group">
          <label>Confidence Threshold:</label>
          <input type="range" id="confidenceSlider" min="0.1" max="1.0" step="0.1" value="${this.mlOptions.confidenceThreshold}">
          <span class="ml-value-display">${(this.mlOptions.confidenceThreshold * 100).toFixed(0)}%</span>
        </div>
        
        <div class="ml-option-group">
          <label>Risk Level:</label>
          <select id="riskLevelSelect">
            <option value="conservative" ${this.mlOptions.riskLevel === 'conservative' ? 'selected' : ''}>Conservative</option>
            <option value="medium" ${this.mlOptions.riskLevel === 'medium' ? 'selected' : ''}>Medium</option>
            <option value="aggressive" ${this.mlOptions.riskLevel === 'aggressive' ? 'selected' : ''}>Aggressive</option>
            <option value="degen" ${this.mlOptions.riskLevel === 'degen' ? 'selected' : ''}>Degen Mode 🚀</option>
          </select>
        </div>
        
        <div class="ml-option-group">
          <label>Time Horizon:</label>
          <select id="timeHorizonSelect">
            <option value="scalp" ${this.mlOptions.timeHorizon === 'scalp' ? 'selected' : ''}>Scalping (1-5m)</option>
            <option value="short" ${this.mlOptions.timeHorizon === 'short' ? 'selected' : ''}>Short Term (15m-1h)</option>
            <option value="medium" ${this.mlOptions.timeHorizon === 'medium' ? 'selected' : ''}>Medium Term (4h-1d)</option>
            <option value="long" ${this.mlOptions.timeHorizon === 'long' ? 'selected' : ''}>Long Term (1d+)</option>
          </select>
        </div>
        
        <div class="ml-predictions-panel">
          <h4>🎯 Entry Predictions</h4>
          <div class="prediction-row">
            <div class="prediction-long">
              <span class="prediction-label">📈 LONG</span>
              <span class="prediction-price" id="longPredictionPrice">$0.00000000</span>
              <span class="prediction-confidence" id="longPredictionConfidence">0%</span>
            </div>
            <div class="prediction-short">
              <span class="prediction-label">📉 SHORT</span>
              <span class="prediction-price" id="shortPredictionPrice">$0.00000000</span>
              <span class="prediction-confidence" id="shortPredictionConfidence">0%</span>
            </div>
          </div>
          <div class="prediction-reasoning" id="predictionReasoning">
            Analyzing market conditions...
          </div>
        </div>
        
        <div class="ml-actions-panel">
          <h4>🎬 Actionable Signals</h4>
          <div class="action-signals" id="actionSignals">
            <div class="action-signal waiting">
              <span class="action-icon">⏳</span>
              <span class="action-text">Waiting for signal convergence...</span>
            </div>
          </div>
        </div>
      </div>
    `;

    this.setupMLOptionsEvents();
    this.applyMLOptionsStyles();
  }

  setupMLOptionsEvents() {
    // 🚀 FORCE ML OPTIONS TO BE ALWAYS VISIBLE AND ACTIVE
    const toggleBtn = document.getElementById('toggleMLOptions');
    const content = document.getElementById('mlOptionsContent');

    if (toggleBtn && content) {
      // 💪 ENSURE CONTENT IS ALWAYS EXPANDED AND VISIBLE
      content.classList.remove('collapsed');
      content.style.display = 'block';
      content.style.opacity = '1';
      content.style.maxHeight = 'none';
      toggleBtn.textContent = '✅ Active';

      toggleBtn.addEventListener('click', () => {
        const isCollapsed = content.classList.contains('collapsed');
        if (isCollapsed) {
          content.classList.remove('collapsed');
          content.style.display = 'block';
          content.style.opacity = '1';
          content.style.maxHeight = 'none';
          toggleBtn.textContent = '✅ Active';
        } else {
          content.classList.add('collapsed');
          toggleBtn.textContent = '⚙️ Options';
        }
        console.log(`[AdvancedML] 🎛️ ML Options panel ${isCollapsed ? 'EXPANDED' : 'COLLAPSED'}`);
      });
    } else {
      console.warn('[AdvancedML] ⚠️ ML Options elements not found!', { toggleBtn: !!toggleBtn, content: !!content });
    }

    // Admiral mode toggle
    const admiralToggle = document.getElementById('admiralModeToggle');
    if (admiralToggle) {
      admiralToggle.addEventListener('change', (e) => {
        this.admiralMode = e.target.checked;
        console.log(`[AdvancedML] Admiral Mode ${this.admiralMode ? 'ENABLED' : 'DISABLED'}`);
        this.updateAdmiralModeEffects();
      });
    }

    // Confidence threshold slider with enhanced functionality
    const confidenceSlider = document.getElementById('confidenceSlider');
    if (confidenceSlider) {
      confidenceSlider.addEventListener('input', (e) => {
        this.mlOptions.confidenceThreshold = parseFloat(e.target.value);
        const display = e.target.nextElementSibling;
        if (display) {
          display.textContent = `${(this.mlOptions.confidenceThreshold * 100).toFixed(0)}%`;
        }

        console.log(`[AdvancedML] 🎯 Confidence threshold updated to: ${(this.mlOptions.confidenceThreshold * 100).toFixed(0)}%`);

        // Regenerate predictions immediately
        this.generateAdvancedPredictions();

        // Show visual feedback
        this.showMLOptionFeedback('confidence', this.mlOptions.confidenceThreshold);
      });
    } else {
      console.warn('[AdvancedML] Confidence slider not found');
    }

    // Risk level select with enhanced functionality
    const riskSelect = document.getElementById('riskLevelSelect');
    if (riskSelect) {
      riskSelect.addEventListener('change', (e) => {
        this.mlOptions.riskLevel = e.target.value;
        console.log(`[AdvancedML] 🎯 Risk level changed to: ${this.mlOptions.riskLevel}`);

        // Regenerate predictions with new risk level
        this.generateAdvancedPredictions();

        // Show visual feedback
        this.showMLOptionFeedback('riskLevel', e.target.value);
      });
    } else {
      console.warn('[AdvancedML] Risk level select not found');
    }

    // Time horizon select
    const timeHorizonSelect = document.getElementById('timeHorizonSelect');
    if (timeHorizonSelect) {
      timeHorizonSelect.addEventListener('change', (e) => {
        this.mlOptions.timeHorizon = e.target.value;
        console.log(`[AdvancedML] Time horizon changed to: ${this.mlOptions.timeHorizon}`);
      });
    }
  }

  setupAdmiralModeIntegration() {
    console.log('[AdvancedML] Setting up Admiral mode integration...');
    
    // Listen for strategy changes
    document.addEventListener('strategyChanged', (event) => {
      this.currentStrategy = event.detail?.strategy || 'admiral_toa';
      console.log(`[AdvancedML] Strategy changed to: ${this.currentStrategy}`);
      
      if (this.admiralMode) {
        this.updateThresholdLogic();
      }
    });

    // Listen for threshold changes
    document.addEventListener('thresholdChanged', (event) => {
      if (this.admiralMode && event.detail) {
        this.processThresholdUpdate(event.detail);
      }
    });
  }

  updateAdmiralModeEffects() {
    if (this.admiralMode) {
      console.log('[AdvancedML] Activating Admiral Mode effects...');
      
      // Add visual effects for Admiral mode
      document.body.classList.add('admiral-mode-active');
      
      // Update ML predictions based on 5-color thresholds
      this.updateThresholdLogic();
      
      // Enhance visual feedback
      this.activateAdmiralVisuals();
    } else {
      console.log('[AdvancedML] Deactivating Admiral Mode effects...');
      document.body.classList.remove('admiral-mode-active');
    }
  }

  updateThresholdLogic() {
    if (!this.admiralMode) return;
    
    console.log('[AdvancedML] Updating threshold logic for Admiral mode...');
    
    // Get current strategy thresholds
    const strategy = window.TRADING_STRATEGIES?.[this.currentStrategy];
    if (!strategy) return;
    
    // Apply 5-color logic to ML predictions
    const thresholds = this.getCurrentThresholds();
    this.applyThresholdLogicToML(thresholds);
  }

  getCurrentThresholds() {
    // Get current threshold values from the UI
    const thresholds = {};
    
    const indicators = ['rsi', 'macd', 'bb', 'ema', 'sma', 'atr', 'volume'];
    indicators.forEach(indicator => {
      const sliders = document.querySelectorAll(`[data-indicator="${indicator}"] .threshold-slider`);
      if (sliders.length >= 4) {
        thresholds[indicator] = {
          green: parseFloat(sliders[0].value) || 20,
          blue: parseFloat(sliders[1].value) || 40,
          orange: parseFloat(sliders[2].value) || 60,
          red: parseFloat(sliders[3].value) || 80
        };
      }
    });
    
    return thresholds;
  }

  applyThresholdLogicToML(thresholds) {
    console.log('[AdvancedML] Applying 5-color threshold logic to ML predictions...');
    
    // This will be called by the ML prediction engine
    this.thresholdLogic = thresholds;
    
    // Update predictions immediately
    this.generateAdvancedPredictions();
  }

  generateAdvancedPredictions() {
    console.log('[AdvancedML] 🚀 Generating advanced ML predictions...');

    try {
      // Get latest market data
      const latestData = this.getLatestMarketData();
      if (!latestData) {
        console.warn('[AdvancedML] No market data available, generating with defaults');
        // Generate with default data
        const defaultData = this.generateDefaultMarketData();

        // Process default data through the same pipeline
        const longPrediction = this.calculateLongEntry(defaultData);
        const shortPrediction = this.calculateShortEntry(defaultData);
        this.updatePredictionDisplay(longPrediction, shortPrediction);
        this.generateActionableSignals(defaultData, longPrediction, shortPrediction);
        this.updateLivePredictor(longPrediction, shortPrediction);
        return;
      }

      console.log('[AdvancedML] ✅ Market data retrieved, processing predictions...');

      // Generate predictions
      const longPrediction = this.calculateLongEntry(latestData);
      const shortPrediction = this.calculateShortEntry(latestData);

      console.log('[AdvancedML] 📊 Predictions calculated:', { longPrediction, shortPrediction });

      // Update UI
      this.updatePredictionDisplay(longPrediction, shortPrediction);

      // Generate actionable signals
      this.generateActionableSignals(latestData, longPrediction, shortPrediction);

      // Update live predictor display
      this.updateLivePredictor(longPrediction, shortPrediction);

      console.log('[AdvancedML] ✅ ML predictions generated and displayed successfully');

    } catch (error) {
      console.error('[AdvancedML] ❌ Error generating predictions:', error);
      this.showMLError('Failed to generate predictions: ' + error.message);
    }
  }

  generateDefaultMarketData() {
    console.log('[AdvancedML] 🔧 Generating default market data for predictions...');

    const currentPrice = window.currentPrice || 120000;
    const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d'];
    const indicators = ['rsi', 'macd', 'stochastic', 'atr', 'volume'];

    const data = {
      currentPrice: currentPrice,
      timestamp: Date.now(),
      selectedSignals: []
    };

    // Generate realistic indicator data for each timeframe
    timeframes.forEach(tf => {
      data[tf] = {};
      indicators.forEach(indicator => {
        data[tf][indicator] = this.generateRealisticIndicatorData(indicator, currentPrice);
      });
    });

    return data;
  }

  generateRealisticIndicatorData(indicator, currentPrice) {
    switch (indicator) {
      case 'rsi':
        return { value: 30 + Math.random() * 40 }; // 30-70 range
      case 'macd':
        return {
          macd: (Math.random() - 0.5) * 1000,
          signal: (Math.random() - 0.5) * 800,
          histogram: (Math.random() - 0.5) * 200
        };
      case 'stochastic':
        return { k: Math.random() * 100, d: Math.random() * 100 };
      case 'atr':
        return { value: currentPrice * 0.01 * (0.5 + Math.random()) };
      case 'volume':
        return { value: 1000000 + Math.random() * 5000000 };
      default:
        return { value: Math.random() * 100 };
    }
  }

  updateLivePredictor(longPrediction, shortPrediction) {
    console.log('[AdvancedML] 📺 Updating live predictor display...');

    let livePredictorContainer = document.getElementById('livePredictorDisplay');
    if (!livePredictorContainer) {
      console.log('[AdvancedML] 🏗️ Creating live predictor container...');
      // Create the container if it doesn't exist
      livePredictorContainer = document.createElement('div');
      livePredictorContainer.id = 'livePredictorDisplay';
      livePredictorContainer.className = 'live-predictor-container';

      // Find a good place to insert it
      const mlContainer = document.getElementById('ml-advanced-options') ||
                         document.querySelector('.ml-advanced-container') ||
                         document.body;
      mlContainer.appendChild(livePredictorContainer);
    }

    // 🛡️ PREVENT CONTAINER FROM DISAPPEARING
    livePredictorContainer.style.display = 'block';
    livePredictorContainer.style.visibility = 'visible';
    livePredictorContainer.style.opacity = '1';

    const timestamp = new Date().toLocaleString();

    livePredictorContainer.innerHTML = `
      <div class="live-predictor-panel">
        <div class="predictor-header">
          <h4>🔮 Live ML Predictions</h4>
          <span class="update-time">${timestamp}</span>
        </div>

        <div class="predictions-grid">
          <div class="prediction-card long-prediction">
            <div class="prediction-type">📈 LONG</div>
            <div class="confidence-score ${longPrediction.confidence > 70 ? 'high' : longPrediction.confidence > 40 ? 'medium' : 'low'}">
              ${longPrediction.confidence.toFixed(1)}%
            </div>
            <div class="entry-price">Entry: $${this.validatePrice(longPrediction.entryPrice).toLocaleString()}</div>
            <div class="target-price">Target: $${this.validatePrice(longPrediction.targetPrice).toLocaleString()}</div>
            <div class="stop-loss">Stop: $${this.validatePrice(longPrediction.stopLoss).toLocaleString()}</div>
          </div>

          <div class="prediction-card short-prediction">
            <div class="prediction-type">📉 SHORT</div>
            <div class="confidence-score ${shortPrediction.confidence > 70 ? 'high' : shortPrediction.confidence > 40 ? 'medium' : 'low'}">
              ${shortPrediction.confidence.toFixed(1)}%
            </div>
            <div class="entry-price">Entry: $${this.validatePrice(shortPrediction.entryPrice).toLocaleString()}</div>
            <div class="target-price">Target: $${this.validatePrice(shortPrediction.targetPrice).toLocaleString()}</div>
            <div class="stop-loss">Stop: $${this.validatePrice(shortPrediction.stopLoss).toLocaleString()}</div>
          </div>
        </div>

        <div class="prediction-reasoning">
          <div class="reasoning-section">
            <strong>Long Reasoning:</strong> ${longPrediction.reasoning.join(', ')}
          </div>
          <div class="reasoning-section">
            <strong>Short Reasoning:</strong> ${shortPrediction.reasoning.join(', ')}
          </div>
        </div>
      </div>
    `;

    console.log('[AdvancedML] ✅ Live predictor display updated');
  }

  showMLError(message) {
    console.error('[AdvancedML] 🚨 ML Error:', message);

    // Show error in UI
    const errorContainer = document.getElementById('mlErrorDisplay');
    if (errorContainer) {
      errorContainer.innerHTML = `
        <div class="ml-error-message">
          <span class="error-icon">⚠️</span>
          <span class="error-text">${message}</span>
        </div>
      `;
      errorContainer.style.display = 'block';

      // Auto-hide after 5 seconds
      setTimeout(() => {
        errorContainer.style.display = 'none';
      }, 5000);
    }
  }

  calculateLongEntry(data) {
    const currentPrice = data.currentPrice || window.currentPrice || 120000;
    let confidence = 30; // Lower base confidence
    let reasoning = [];

    // 🎯 SELECTED SIGNALS PRIORITY ANALYSIS
    if (data.selectedSignals && data.selectedSignals.length > 0) {
      console.log(`[AdvancedML] 🎯 LONG ANALYSIS: Using ${data.selectedSignals.length} selected signals`);

      let bullishSignals = 0;
      let totalSignals = data.selectedSignals.length;

      data.selectedSignals.forEach(signal => {
        const [indicator, timeframe] = signal.split('-');
        const indicatorData = data[timeframe]?.[indicator];

        if (indicatorData) {
          const analysis = this.analyzeIndicatorForLong(indicator, indicatorData);
          if (analysis.bullish) {
            bullishSignals++;
            confidence += analysis.strength;
            reasoning.push(`${indicator.toUpperCase()}(${timeframe}): ${analysis.reason}`);
          }
        }
      });

      // CONVERGENCE MULTIPLIER
      const convergenceRatio = bullishSignals / totalSignals;
      if (convergenceRatio >= 0.8) {
        confidence += 25;
        reasoning.push(`🔥 STRONG CONVERGENCE: ${bullishSignals}/${totalSignals} bullish`);
      } else if (convergenceRatio >= 0.6) {
        confidence += 15;
        reasoning.push(`⚡ MODERATE CONVERGENCE: ${bullishSignals}/${totalSignals} bullish`);
      }

    } else {
      // Fallback analysis when no signals selected
      reasoning.push('⚠️ No signals selected - using basic analysis');

      // Basic RSI analysis
      if (data['1h']?.rsi && data['1h'].rsi.value < 30) {
        confidence += 15;
        reasoning.push('RSI(1h) oversold');
      }

      // Basic MACD analysis
      if (data['1h']?.macd && data['1h'].macd.value > 0) {
        confidence += 10;
        reasoning.push('MACD(1h) bullish');
      }
    }

    // Admiral mode threshold analysis
    if (this.admiralMode && this.thresholdLogic) {
      const thresholdBonus = this.calculateThresholdBonus(data, 'long');
      confidence += thresholdBonus;
      if (thresholdBonus > 0) {
        reasoning.push('Admiral thresholds aligned');
      }
    }

    // Risk level adjustment
    const riskMultiplier = this.getRiskMultiplier();
    confidence *= riskMultiplier;

    // Calculate entry price (slightly below current for better entry)
    const entryPrice = currentPrice * 0.998;

    return {
      price: entryPrice,
      confidence: Math.min(95, Math.max(5, confidence)),
      timeframe: this.getOptimalTimeframe(),
      reasoning: reasoning.join(' | ') || 'Awaiting signal selection'
    };
  }

  // 🎯 INDICATOR ANALYSIS FOR LONG POSITIONS
  analyzeIndicatorForLong(indicator, data) {
    switch (indicator.toLowerCase()) {
      case 'rsi':
        const rsiValue = data.value || 50;
        if (rsiValue <= 20) return { bullish: true, strength: 25, reason: 'Extremely oversold' };
        if (rsiValue <= 30) return { bullish: true, strength: 20, reason: 'Oversold' };
        if (rsiValue <= 40) return { bullish: true, strength: 10, reason: 'Below midline' };
        return { bullish: false, strength: 0, reason: 'Not oversold' };

      case 'stochrsi':
        const stochValue = data.value || 0.5;
        if (stochValue <= 0.2) return { bullish: true, strength: 20, reason: 'Stoch oversold' };
        if (stochValue <= 0.3) return { bullish: true, strength: 15, reason: 'Stoch low' };
        return { bullish: false, strength: 0, reason: 'Stoch not oversold' };

      case 'macd':
        const macdValue = data.macd || data.value || 0;
        if (macdValue > 100) return { bullish: true, strength: 25, reason: 'Strong bullish momentum' };
        if (macdValue > 0) return { bullish: true, strength: 15, reason: 'Bullish momentum' };
        return { bullish: false, strength: 0, reason: 'Bearish momentum' };

      case 'atr':
        const atrValue = data.value || 1;
        if (atrValue < 2) return { bullish: true, strength: 10, reason: 'Low volatility favorable' };
        return { bullish: false, strength: -5, reason: 'High volatility risk' };

      case 'adx':
        const adxValue = data.value || 25;
        if (adxValue > 25) return { bullish: true, strength: 15, reason: 'Strong trend confirmed' };
        return { bullish: false, strength: 0, reason: 'Weak trend' };

      default:
        return { bullish: false, strength: 5, reason: 'Neutral indicator' };
    }
  }

  calculateShortEntry(data) {
    const currentPrice = data.currentPrice || 0;
    let confidence = 50;
    let reasoning = [];
    
    // RSI analysis
    if (data.rsi && data.rsi.value > 70) {
      confidence += 15;
      reasoning.push('RSI overbought');
    }
    
    // MACD analysis
    if (data.macd && data.macd.value < 0) {
      confidence += 10;
      reasoning.push('MACD bearish');
    }
    
    // Admiral mode threshold analysis
    if (this.admiralMode && this.thresholdLogic) {
      const thresholdBonus = this.calculateThresholdBonus(data, 'short');
      confidence += thresholdBonus;
      if (thresholdBonus > 0) {
        reasoning.push('Admiral thresholds aligned');
      }
    }
    
    // Risk level adjustment
    const riskMultiplier = this.getRiskMultiplier();
    confidence *= riskMultiplier;
    
    // Calculate entry price (slightly above current for better entry)
    const entryPrice = currentPrice * 1.002;
    
    return {
      price: entryPrice,
      confidence: Math.min(95, Math.max(5, confidence)),
      timeframe: this.getOptimalTimeframe(),
      reasoning: reasoning.join(', ') || 'Technical analysis'
    };
  }

  calculateThresholdBonus(data, direction) {
    if (!this.thresholdLogic) return 0;
    
    let bonus = 0;
    const indicators = Object.keys(this.thresholdLogic);
    
    indicators.forEach(indicator => {
      const value = data[indicator]?.value;
      const thresholds = this.thresholdLogic[indicator];
      
      if (value !== undefined && thresholds) {
        if (direction === 'long') {
          if (value <= thresholds.green) bonus += 5; // Strong buy signal
          else if (value <= thresholds.blue) bonus += 3; // Moderate buy signal
        } else {
          if (value >= thresholds.red) bonus += 5; // Strong sell signal
          else if (value >= thresholds.orange) bonus += 3; // Moderate sell signal
        }
      }
    });
    
    return bonus;
  }

  getRiskMultiplier() {
    switch (this.mlOptions.riskLevel) {
      case 'conservative': return 0.8;
      case 'medium': return 1.0;
      case 'aggressive': return 1.2;
      case 'degen': return 1.5;
      default: return 1.0;
    }
  }

  getOptimalTimeframe() {
    switch (this.mlOptions.timeHorizon) {
      case 'scalp': return '1m-5m';
      case 'short': return '15m-1h';
      case 'medium': return '4h-1d';
      case 'long': return '1d+';
      default: return '15m-1h';
    }
  }

  updatePredictionDisplay(longPred, shortPred) {
    // 🛡️ BULLETPROOF PREDICTION DISPLAY - PREVENT $NaN ERRORS
    console.log('[AdvancedML] 🎯 Updating prediction display:', { longPred, shortPred });

    // Update long prediction with NaN protection
    const longPrice = document.getElementById('longPredictionPrice');
    const longConf = document.getElementById('longPredictionConfidence');

    if (longPrice) {
      const safePrice = this.validatePrice(longPred.price);
      longPrice.textContent = `$${safePrice.toFixed(8)}`;
    }
    if (longConf) {
      const safeConfidence = this.validateConfidence(longPred.confidence);
      longConf.textContent = `${safeConfidence.toFixed(1)}%`;
      longConf.className = `prediction-confidence ${this.getConfidenceClass(safeConfidence)}`;
    }
    
    // Update short prediction
    const shortPrice = document.getElementById('shortPredictionPrice');
    const shortConf = document.getElementById('shortPredictionConfidence');
    
    if (shortPrice) shortPrice.textContent = `$${shortPred.price.toFixed(8)}`;
    if (shortConf) {
      shortConf.textContent = `${shortPred.confidence.toFixed(1)}%`;
      shortConf.className = `prediction-confidence ${this.getConfidenceClass(shortPred.confidence)}`;
    }
    
    // Update reasoning
    const reasoning = document.getElementById('predictionReasoning');
    if (reasoning) {
      const bestPred = longPred.confidence > shortPred.confidence ? longPred : shortPred;
      const direction = longPred.confidence > shortPred.confidence ? 'LONG' : 'SHORT';
      reasoning.innerHTML = `
        <strong>${direction} Signal:</strong> ${bestPred.reasoning}<br>
        <small>Timeframe: ${bestPred.timeframe} | Confidence: ${bestPred.confidence.toFixed(1)}%</small>
      `;
    }
  }

  getConfidenceClass(confidence) {
    if (confidence >= 80) return 'high-confidence';
    if (confidence >= 60) return 'medium-confidence';
    if (confidence >= 40) return 'low-confidence';
    return 'very-low-confidence';
  }

  generateActionableSignals(data, longPred, shortPred) {
    const signals = [];
    const timestamp = Date.now();

    console.log('[AdvancedML] 🎬 Generating actionable signals...', { longPred, shortPred });

    // 🚀 ALWAYS GENERATE SIGNALS - EVEN WITH LOW CONFIDENCE
    const bestPrediction = longPred.confidence > shortPred.confidence ? longPred : shortPred;
    const direction = longPred.confidence > shortPred.confidence ? 'LONG' : 'SHORT';
    const emoji = direction === 'LONG' ? '🚀' : '📉';

    // Primary signal - always show the best prediction
    signals.push({
      id: `primary-${timestamp}`,
      type: 'entry',
      direction: direction.toLowerCase(),
      action: `${emoji} ${direction} SIGNAL • ${bestPrediction.confidence.toFixed(1)}% CONFIDENCE`,
      confidence: bestPrediction.confidence,
      urgency: this.calculateUrgency(bestPrediction.confidence),
      timeframe: bestPrediction.timeframe || this.getOptimalTimeframe()
    });

    // 🎯 CONVERGENCE DETECTION - Check for signal alignment
    const convergenceCount = this.detectSignalConvergence(data);
    if (convergenceCount > 0) {
      signals.push({
        id: `convergence-${timestamp}`,
        type: 'convergence',
        action: `⚡ ${convergenceCount} SIGNALS CONVERGING • ${direction} BIAS`,
        confidence: Math.min(95, bestPrediction.confidence + (convergenceCount * 5)),
        urgency: convergenceCount >= 3 ? 'high' : 'medium'
      });
    }

    // 🛡️ RISK MANAGEMENT - Always provide risk guidance
    const currentPrice = data.currentPrice || window.currentPrice || 120000;
    const riskLevel = this.calculateRiskLevel(data);

    signals.push({
      id: `risk-${timestamp}`,
      type: 'risk',
      action: `🛡️ RISK LEVEL: ${riskLevel.toUpperCase()} • POSITION SIZE: ${this.getPositionSize(riskLevel)}%`,
      confidence: 90,
      urgency: riskLevel === 'high' ? 'high' : 'low'
    });

    // 📊 MARKET CONDITIONS
    const marketCondition = this.analyzeMarketCondition(data);
    signals.push({
      id: `market-${timestamp}`,
      type: 'analysis',
      action: `📊 MARKET: ${marketCondition.condition} • VOLATILITY: ${marketCondition.volatility}`,
      confidence: 85,
      urgency: 'low'
    });

    console.log(`[AdvancedML] ✅ Generated ${signals.length} actionable signals`);
    this.updateActionSignalsDisplay(signals);
  }

  calculateUrgency(confidence) {
    if (confidence >= 90) return 'high';
    if (confidence >= 70) return 'medium';
    return 'low';
  }

  // 🎯 SIGNAL CONVERGENCE DETECTION - EXTREME PRECISION
  detectSignalConvergence(data) {
    let convergenceCount = 0;
    const timeframes = ['15m', '1h', '4h'];
    const indicators = ['rsi', 'macd', 'stochRsi'];

    timeframes.forEach(tf => {
      if (data[tf]) {
        indicators.forEach(indicator => {
          const indicatorData = data[tf][indicator];
          if (indicatorData && (indicatorData.signal === 'strong_buy' || indicatorData.signal === 'strong_sell')) {
            convergenceCount++;
          }
        });
      }
    });

    return convergenceCount;
  }

  // 🛡️ RISK LEVEL CALCULATION - MAXIMUM PROTECTION
  calculateRiskLevel(data) {
    const volatility = this.calculateVolatility(data);
    const trendStrength = this.calculateTrendStrength(data);

    if (volatility > 0.8 || trendStrength < 0.3) return 'high';
    if (volatility > 0.5 || trendStrength < 0.6) return 'medium';
    return 'low';
  }

  // 📊 POSITION SIZE RECOMMENDATION
  getPositionSize(riskLevel) {
    switch (riskLevel) {
      case 'low': return 5;
      case 'medium': return 3;
      case 'high': return 1;
      default: return 2;
    }
  }

  // 🌊 MARKET CONDITION ANALYSIS
  analyzeMarketCondition(data) {
    const volatility = this.calculateVolatility(data);
    const trend = this.calculateTrendStrength(data);

    let condition = 'RANGING';
    if (trend > 0.7) condition = 'TRENDING';
    if (volatility > 0.8) condition = 'VOLATILE';
    if (trend > 0.8 && volatility < 0.3) condition = 'STRONG TREND';

    return {
      condition,
      volatility: volatility > 0.7 ? 'HIGH' : volatility > 0.4 ? 'MEDIUM' : 'LOW'
    };
  }

  // 📈 VOLATILITY CALCULATION
  calculateVolatility(data) {
    if (data.atr && data.atr.value) {
      return Math.min(1.0, data.atr.value / 1000); // Normalize ATR
    }
    return 0.5; // Default medium volatility
  }

  // 💪 TREND STRENGTH CALCULATION
  calculateTrendStrength(data) {
    let strength = 0.5;

    if (data['1h'] && data['1h'].macd) {
      const macd = data['1h'].macd;
      if (macd.histogram > 0.001) strength += 0.2;
      if (macd.histogram < -0.001) strength += 0.2;
    }

    if (data['4h'] && data['4h'].rsi) {
      const rsi = data['4h'].rsi;
      if (rsi.value > 70 || rsi.value < 30) strength += 0.3;
    }

    return Math.min(1.0, strength);
  }

  // 🛡️ PRICE VALIDATION - PREVENT $NaN ERRORS
  validatePrice(price) {
    if (typeof price !== 'number' || isNaN(price) || !isFinite(price)) {
      console.warn('[AdvancedML] ⚠️ Invalid price detected, using fallback:', price);
      return window.currentPrice || 120000; // Use current price or default
    }
    if (price <= 0) {
      console.warn('[AdvancedML] ⚠️ Negative/zero price detected, using fallback:', price);
      return window.currentPrice || 120000;
    }
    return price;
  }

  // 🛡️ CONFIDENCE VALIDATION - PREVENT NaN PERCENTAGES
  validateConfidence(confidence) {
    if (typeof confidence !== 'number' || isNaN(confidence) || !isFinite(confidence)) {
      console.warn('[AdvancedML] ⚠️ Invalid confidence detected, using fallback:', confidence);
      return 50; // Default 50% confidence
    }
    return Math.max(0, Math.min(100, confidence)); // Clamp between 0-100
  }

  // 🛡️ SAFE PRICE CALCULATION
  calculateSafePrice(basePrice, multiplier = 1.0) {
    const safeBase = this.validatePrice(basePrice);
    const safeMultiplier = (typeof multiplier === 'number' && isFinite(multiplier)) ? multiplier : 1.0;
    return safeBase * safeMultiplier;
  }

  updateActionSignalsDisplay(signals) {
    const container = document.getElementById('actionSignals');
    if (!container) {
      console.warn('[AdvancedML] ⚠️ Action signals container not found!');
      return;
    }

    // 🚀 ANTI-FLASH PROTECTION - Store previous content to prevent unnecessary updates
    const currentContent = container.innerHTML;
    let newContent;

    if (signals.length === 0) {
      newContent = `
        <div class="action-signal waiting stable-signal">
          <span class="action-icon">⏳</span>
          <span class="action-text">SCANNING FOR CONVERGENCE • LIVE ANALYSIS ACTIVE</span>
        </div>
      `;
    } else {
      newContent = signals.map(signal => `
        <div class="action-signal ${signal.urgency}-urgency stable-signal" data-signal-id="${signal.id || Math.random()}">
          <span class="action-icon">${this.getSignalIcon(signal.type, signal.urgency)}</span>
          <span class="action-text">${signal.action}</span>
          <span class="action-confidence">${signal.confidence.toFixed(1)}%</span>
          <span class="action-timestamp">${new Date().toLocaleTimeString()}</span>
        </div>
      `).join('');
    }

    // 🛡️ ONLY UPDATE IF CONTENT ACTUALLY CHANGED - PREVENTS FLASHING
    if (currentContent.trim() !== newContent.trim()) {
      container.innerHTML = newContent;
      console.log(`[AdvancedML] 🎬 Updated actionable signals: ${signals.length} signals`);
    }
  }

  getSignalIcon(type, urgency) {
    if (urgency === 'high') return '🔥';
    if (type === 'entry') return '🎯';
    if (type === 'risk') return '🛡️';
    return '📊';
  }

  getLatestMarketData() {
    // 🚀 ENHANCED DATA COLLECTION - INCLUDE SELECTED SIGNALS
    const marketData = {};

    // Get latest data from global indicators
    if (window.indicatorsData && Object.keys(window.indicatorsData).length > 0) {
      const timeframes = Object.keys(window.indicatorsData);

      // Collect data from all timeframes
      timeframes.forEach(tf => {
        marketData[tf] = window.indicatorsData[tf];
      });

      // Get selected signals from ML system
      const mlSystem = window.MLHistoricalAnalysis || window.mlHistoricalAnalysis;
      if (mlSystem && mlSystem.selectedLights && mlSystem.selectedLights.size > 0) {
        marketData.selectedSignals = Array.from(mlSystem.selectedLights);
        console.log(`[AdvancedML] 🎯 Using ${mlSystem.selectedLights.size} selected signals for predictions`);
      } else {
        // Fallback: get selected signals from DOM
        const selectedLights = document.querySelectorAll('.signal-circle.selected, .signal-light.selected');
        if (selectedLights.length > 0) {
          marketData.selectedSignals = Array.from(selectedLights).map(light => {
            const indicator = light.getAttribute('data-connected-indicator') ||
                             light.getAttribute('data-indicator') ||
                             light.getAttribute('data-ind') || 'unknown';
            const timeframe = light.getAttribute('data-connected-timeframe') ||
                             light.getAttribute('data-timeframe') ||
                             light.getAttribute('data-tf') || 'unknown';
            return `${indicator}-${timeframe}`;
          });
          console.log(`[AdvancedML] 🎯 Using ${selectedLights.length} DOM selected signals for predictions`);
        }
      }

      // Add current price data
      marketData.currentPrice = window.currentPrice || 0;
      marketData.timestamp = Date.now();

      return marketData;
    }

    return null;
  }

  activateAdmiralVisuals() {
    console.log('[AdvancedML] Activating Admiral visual effects...');
    
    // Add pulsing effect to signal lights when Admiral mode is active
    const signalLights = document.querySelectorAll('.signal-light, .circle');
    signalLights.forEach(light => {
      light.classList.add('admiral-enhanced');
    });
  }

  initializeActionableSignals() {
    // Start generating actionable signals immediately and every 3 seconds
    this.generateAdvancedPredictions(); // Initial call

    setInterval(() => {
      this.generateAdvancedPredictions(); // Always generate predictions
    }, 3000); // More frequent updates
  }

  setupVisualEffects() {
    // Add visual effect styles
    this.applyMLOptionsStyles();
  }

  startMLAnalysisEngine() {
    console.log('[AdvancedML] Starting ML analysis engine...');
    
    // Initial prediction generation
    setTimeout(() => {
      this.generateAdvancedPredictions();
    }, 2000);
  }

  applyMLOptionsStyles() {
    const style = document.createElement('style');
    style.textContent = `
      /* Advanced ML Options Styling */
      .ml-advanced-container {
        background: rgba(0, 10, 20, 0.9);
        border: 1px solid rgba(0, 255, 255, 0.3);
        border-radius: 8px;
        margin: 10px 0;
        padding: 15px;
        font-family: 'Courier New', monospace;
      }
      
      .ml-options-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        border-bottom: 1px solid rgba(0, 255, 255, 0.2);
        padding-bottom: 10px;
      }
      
      .ml-options-header h3 {
        color: #00d4ff;
        margin: 0;
        font-size: 16px;
      }
      
      .ml-toggle-button {
        background: linear-gradient(135deg, #00d4ff, #0099cc);
        border: none;
        color: #000;
        padding: 5px 10px;
        border-radius: 4px;
        cursor: pointer;
        font-size: 12px;
        font-weight: bold;
      }
      
      .ml-options-content {
        transition: all 0.3s ease;
        overflow: hidden;
      }
      
      .ml-options-content.collapsed {
        max-height: 0;
        opacity: 0;
        padding: 0;
      }
      
      .ml-option-group {
        margin-bottom: 15px;
        display: flex;
        align-items: center;
        gap: 10px;
      }
      
      .ml-option-label {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #ffffff;
        cursor: pointer;
      }
      
      .ml-checkbox-custom {
        width: 18px;
        height: 18px;
        border: 2px solid #00d4ff;
        border-radius: 3px;
        position: relative;
      }
      
      .ml-option-group input[type="checkbox"]:checked + .ml-checkbox-custom::after {
        content: '✓';
        position: absolute;
        top: -2px;
        left: 2px;
        color: #00d4ff;
        font-weight: bold;
      }
      
      .ml-option-group input[type="range"] {
        flex: 1;
        margin: 0 10px;
      }
      
      .ml-value-display {
        color: #00ff88;
        font-weight: bold;
        min-width: 40px;
      }
      
      .ml-option-group select {
        background: rgba(0, 20, 40, 0.8);
        border: 1px solid rgba(0, 255, 255, 0.3);
        color: #ffffff;
        padding: 5px 10px;
        border-radius: 4px;
        flex: 1;
      }
      
      .ml-predictions-panel, .ml-actions-panel {
        background: rgba(0, 20, 40, 0.5);
        border: 1px solid rgba(0, 255, 255, 0.2);
        border-radius: 6px;
        padding: 12px;
        margin: 10px 0;
      }
      
      .ml-predictions-panel h4, .ml-actions-panel h4 {
        color: #00d4ff;
        margin: 0 0 10px 0;
        font-size: 14px;
      }
      
      .prediction-row {
        display: flex;
        gap: 20px;
        margin-bottom: 10px;
      }
      
      .prediction-long, .prediction-short {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 5px;
      }
      
      .prediction-label {
        font-weight: bold;
        font-size: 12px;
      }
      
      .prediction-price {
        color: #00ff88;
        font-weight: bold;
        font-size: 14px;
      }
      
      .prediction-confidence {
        font-size: 12px;
        font-weight: bold;
      }
      
      .prediction-confidence.high-confidence { color: #00ff00; }
      .prediction-confidence.medium-confidence { color: #ffaa00; }
      .prediction-confidence.low-confidence { color: #ff6600; }
      .prediction-confidence.very-low-confidence { color: #ff0000; }
      
      .prediction-reasoning {
        background: rgba(0, 0, 0, 0.3);
        padding: 8px;
        border-radius: 4px;
        font-size: 11px;
        color: #cccccc;
        line-height: 1.4;
      }
      
      .action-signal {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        margin: 8px 0;
        border-radius: 6px;
        font-size: 13px;
        background: rgba(0, 20, 40, 0.8);
        border-left: 4px solid #00d4ff;
        transition: all 0.3s ease;
        position: relative;
        overflow: hidden;
      }

      /* 🚀 STABLE SIGNAL STYLES - ANTI-FLASH PROTECTION */
      .action-signal.stable-signal {
        animation: none !important;
        opacity: 1 !important;
        transform: none !important;
      }

      .action-signal.waiting {
        background: rgba(20, 20, 20, 0.8);
        color: #cccccc;
        border-left-color: #888888;
        animation: pulse-waiting 2s infinite;
      }

      @keyframes pulse-waiting {
        0%, 100% { opacity: 0.7; }
        50% { opacity: 1.0; }
      }

      .action-signal.high-urgency {
        background: rgba(40, 0, 0, 0.8);
        border-left-color: #ff4444;
        color: #ffffff;
        box-shadow: 0 0 10px rgba(255, 68, 68, 0.3);
        animation: pulse-urgent 1.5s infinite;
      }

      @keyframes pulse-urgent {
        0%, 100% { box-shadow: 0 0 10px rgba(255, 68, 68, 0.3); }
        50% { box-shadow: 0 0 20px rgba(255, 68, 68, 0.6); }
      }

      .action-signal.medium-urgency {
        background: rgba(40, 20, 0, 0.8);
        border-left-color: #ffaa00;
        color: #ffffff;
        box-shadow: 0 0 8px rgba(255, 170, 0, 0.2);
      }

      .action-signal.low-urgency {
        background: rgba(0, 40, 20, 0.8);
        border-left-color: #00ff88;
        color: #ffffff;
        box-shadow: 0 0 6px rgba(0, 255, 136, 0.2);
      }
      
      .action-confidence {
        margin-left: auto;
        font-weight: bold;
        color: #00d4ff;
        font-size: 12px;
      }

      .action-timestamp {
        font-size: 10px;
        color: #888888;
        margin-left: auto;
        opacity: 0.7;
      }

      .action-icon {
        font-size: 16px;
        min-width: 20px;
        text-align: center;
      }

      .action-text {
        flex: 1;
        font-weight: 500;
        line-height: 1.3;
      }
      
      /* Admiral Mode Effects */
      body.admiral-mode-active .signal-light.admiral-enhanced {
        box-shadow: 0 0 15px currentColor;
        animation: admiralPulse 2s infinite;
      }
      
      @keyframes admiralPulse {
        0%, 100% { transform: scale(1); opacity: 1; }
        50% { transform: scale(1.05); opacity: 0.8; }
      }
      
      @keyframes pulse {
        0%, 100% { opacity: 1; }
        50% { opacity: 0.7; }
      }
    `;
    document.head.appendChild(style);
  }
}

// Initialize advanced ML features
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    console.log('[AdvancedML] Initializing Advanced ML Features...');
    window.advancedMLFeatures = new AdvancedMLFeatures();
    console.log('[AdvancedML] Advanced ML Features initialized and running');
  }, 2000); // Start sooner
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = AdvancedMLFeatures;
}
