/**
 * Enhanced Signal Logic Menu for StarCrypt
 * Provides comprehensive signal logic configuration interface
 */

class EnhancedSignalLogicMenu {
  constructor() {
    this.isVisible = false;
    this.currentTab = 'convergence';
    this.init();
  }
  
  init() {
    this.createMenuStructure();
    this.setupEventListeners();
    console.log('🎛️ Enhanced Signal Logic Menu initialized');
  }
  
  createMenuStructure() {
    const menuContainer = document.getElementById('logicMenu');
    if (!menuContainer) {
      console.warn('Logic menu container not found');
      return;
    }
    
    menuContainer.innerHTML = this.generateMenuHTML();
    this.populateMenuContent();
  }
  
  generateMenuHTML() {
    return `
      <div class="enhanced-signal-logic-menu">
        <div class="menu-header">
          <h3>🔮 Enhanced Signal Logic</h3>
          <div class="menu-tabs">
            <button class="tab-btn active" data-tab="convergence">Convergence</button>
            <button class="tab-btn" data-tab="lightlogic">Light Logic</button>
            <button class="tab-btn" data-tab="advanced">Advanced</button>
          </div>
        </div>
        
        <div class="menu-content">
          <!-- Convergence Tab -->
          <div class="tab-content active" id="convergence-tab">
            <div class="setting-group">
              <label>Minimum Convergence Indicators</label>
              <div class="slider-container">
                <input type="range" id="minConvergence" min="1" max="10" value="3" class="enhanced-slider">
                <span class="slider-value">3</span>
              </div>
            </div>
            
            <div class="setting-group">
              <label>Convergence Threshold</label>
              <div class="slider-container">
                <input type="range" id="convergenceThreshold" min="0.1" max="1" step="0.1" value="0.7" class="enhanced-slider">
                <span class="slider-value">70%</span>
              </div>
            </div>
            
            <div class="setting-group">
              <label>Signal Priority</label>
              <select id="signalPriority" class="enhanced-select">
                <option value="balanced">Balanced</option>
                <option value="strong">Strong Signals</option>
                <option value="mild">Mild Signals</option>
              </select>
            </div>
            
            <div class="setting-group">
              <label>Signal Weights</label>
              <div class="weight-controls">
                <div class="weight-item">
                  <span>Strong Signals:</span>
                  <input type="number" id="strongWeight" min="1" max="5" value="2" step="0.1">
                </div>
                <div class="weight-item">
                  <span>Mild Signals:</span>
                  <input type="number" id="mildWeight" min="0.1" max="2" value="1" step="0.1">
                </div>
              </div>
            </div>
          </div>
          
          <!-- Light Logic Tab -->
          <div class="tab-content" id="lightlogic-tab">
            <div class="setting-group">
              <label>Light Logic Style</label>
              <select id="lightLogicStyle" class="enhanced-select">
                <option value="conservative">Conservative</option>
                <option value="wallstreet">Wall Street</option>
                <option value="vibeflow">Vibe Flow</option>
                <option value="cosmic">Cosmic</option>
                <option value="chaos">Chaos</option>
              </select>
            </div>
            
            <div class="setting-group">
              <label>Light Intensity</label>
              <div class="slider-container">
                <input type="range" id="lightIntensity" min="1" max="5" value="3" class="enhanced-slider">
                <span class="slider-value">3</span>
              </div>
            </div>
            
            <div class="setting-group">
              <label>Visual Effects</label>
              <div class="checkbox-group">
                <label class="checkbox-item">
                  <input type="checkbox" id="enablePulse" checked>
                  <span class="checkmark"></span>
                  Pulse Animation
                </label>
                <label class="checkbox-item">
                  <input type="checkbox" id="enableGlow" checked>
                  <span class="checkmark"></span>
                  Glow Effects
                </label>
                <label class="checkbox-item">
                  <input type="checkbox" id="enableChevrons" checked>
                  <span class="checkmark"></span>
                  Chevron Indicators
                </label>
                <label class="checkbox-item">
                  <input type="checkbox" id="enableDotMatrix" checked>
                  <span class="checkmark"></span>
                  Dot Matrix
                </label>
              </div>
            </div>
          </div>
          
          <!-- Advanced Tab -->
          <div class="tab-content" id="advanced-tab">
            <div class="setting-group">
              <label>Update Interval (ms)</label>
              <div class="slider-container">
                <input type="range" id="updateInterval" min="50" max="1000" step="50" value="100" class="enhanced-slider">
                <span class="slider-value">100ms</span>
              </div>
            </div>
            
            <div class="setting-group">
              <label>Convergence History</label>
              <div class="history-controls">
                <button id="clearHistory" class="btn-secondary">Clear History</button>
                <button id="exportHistory" class="btn-secondary">Export Data</button>
              </div>
            </div>
            
            <div class="setting-group">
              <label>Debug Mode</label>
              <label class="checkbox-item">
                <input type="checkbox" id="debugMode">
                <span class="checkmark"></span>
                Enable Debug Logging
              </label>
            </div>
            
            <div class="setting-group">
              <label>Reset Settings</label>
              <button id="resetSettings" class="btn-danger">Reset to Defaults</button>
            </div>
          </div>
        </div>
        
        <div class="menu-footer">
          <div class="status-indicators">
            <div class="status-item">
              <span class="status-label">Convergence:</span>
              <span class="convergence-strength">--</span>
            </div>
            <div class="status-item">
              <span class="status-label">Confidence:</span>
              <span class="confidence-level">--</span>
            </div>
          </div>
          
          <div class="menu-actions">
            <button id="applySettings" class="btn-primary">Apply Settings</button>
            <button id="savePreset" class="btn-secondary">Save Preset</button>
          </div>
        </div>
      </div>
    `;
  }
  
  populateMenuContent() {
    // Load current settings from enhanced signal logic
    if (window.enhancedSignalLogic) {
      this.loadCurrentSettings();
    }
    
    // Setup real-time updates
    this.setupRealTimeUpdates();
  }
  
  loadCurrentSettings() {
    const signalLogic = window.enhancedSignalLogic;
    const convergenceSettings = signalLogic.convergenceSettings;
    const lightLogicSettings = signalLogic.lightLogicSettings;
    
    // Load convergence settings
    document.getElementById('minConvergence').value = convergenceSettings.minConvergence;
    document.getElementById('convergenceThreshold').value = convergenceSettings.convergenceThreshold;
    document.getElementById('signalPriority').value = convergenceSettings.priority;
    document.getElementById('strongWeight').value = convergenceSettings.strongSignalWeight;
    document.getElementById('mildWeight').value = convergenceSettings.mildSignalWeight;
    
    // Load light logic settings
    document.getElementById('lightLogicStyle').value = lightLogicSettings.style;
    document.getElementById('lightIntensity').value = lightLogicSettings.intensity;
    document.getElementById('enablePulse').checked = lightLogicSettings.enablePulse;
    document.getElementById('enableGlow').checked = lightLogicSettings.enableGlow;
    document.getElementById('enableChevrons').checked = lightLogicSettings.enableChevrons;
    document.getElementById('enableDotMatrix').checked = lightLogicSettings.enableDotMatrix;
    
    // Load advanced settings
    document.getElementById('updateInterval').value = signalLogic.updateInterval;
    
    // Update slider displays
    this.updateSliderDisplays();
  }
  
  setupEventListeners() {
    // Tab switching
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.addEventListener('click', (e) => {
        this.switchTab(e.target.dataset.tab);
      });
    });
    
    // Slider updates
    document.querySelectorAll('.enhanced-slider').forEach(slider => {
      slider.addEventListener('input', (e) => {
        this.updateSliderDisplay(e.target);
        this.handleSettingChange();
      });
    });
    
    // Select changes
    document.querySelectorAll('.enhanced-select').forEach(select => {
      select.addEventListener('change', () => {
        this.handleSettingChange();
      });
    });
    
    // Checkbox changes
    document.querySelectorAll('input[type="checkbox"]').forEach(checkbox => {
      checkbox.addEventListener('change', () => {
        this.handleSettingChange();
      });
    });
    
    // Number input changes
    document.querySelectorAll('input[type="number"]').forEach(input => {
      input.addEventListener('change', () => {
        this.handleSettingChange();
      });
    });
    
    // Button actions
    document.getElementById('applySettings')?.addEventListener('click', () => {
      this.applySettings();
    });
    
    document.getElementById('savePreset')?.addEventListener('click', () => {
      this.savePreset();
    });
    
    document.getElementById('clearHistory')?.addEventListener('click', () => {
      this.clearHistory();
    });
    
    document.getElementById('exportHistory')?.addEventListener('click', () => {
      this.exportHistory();
    });
    
    document.getElementById('resetSettings')?.addEventListener('click', () => {
      this.resetSettings();
    });
  }
  
  setupRealTimeUpdates() {
    // Update status indicators every second
    setInterval(() => {
      this.updateStatusIndicators();
    }, 1000);
  }
  
  switchTab(tabName) {
    // Update tab buttons
    document.querySelectorAll('.tab-btn').forEach(btn => {
      btn.classList.remove('active');
    });
    document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
    
    // Update tab content
    document.querySelectorAll('.tab-content').forEach(content => {
      content.classList.remove('active');
    });
    document.getElementById(`${tabName}-tab`).classList.add('active');
    
    this.currentTab = tabName;
  }
  
  updateSliderDisplay(slider) {
    const valueSpan = slider.parentElement.querySelector('.slider-value');
    if (!valueSpan) return;
    
    let displayValue = slider.value;
    
    // Format specific sliders
    switch (slider.id) {
      case 'convergenceThreshold':
        displayValue = Math.round(slider.value * 100) + '%';
        break;
      case 'updateInterval':
        displayValue = slider.value + 'ms';
        break;
    }
    
    valueSpan.textContent = displayValue;
  }
  
  updateSliderDisplays() {
    document.querySelectorAll('.enhanced-slider').forEach(slider => {
      this.updateSliderDisplay(slider);
    });
  }
  
  handleSettingChange() {
    // Apply settings in real-time if enabled
    if (document.getElementById('debugMode')?.checked) {
      this.applySettings();
    }
  }
  
  applySettings() {
    if (!window.enhancedSignalLogic) return;
    
    // Collect convergence settings
    const convergenceSettings = {
      minConvergence: parseInt(document.getElementById('minConvergence').value),
      convergenceThreshold: parseFloat(document.getElementById('convergenceThreshold').value),
      priority: document.getElementById('signalPriority').value,
      strongSignalWeight: parseFloat(document.getElementById('strongWeight').value),
      mildSignalWeight: parseFloat(document.getElementById('mildWeight').value)
    };
    
    // Collect light logic settings
    const lightLogicSettings = {
      style: document.getElementById('lightLogicStyle').value,
      intensity: parseInt(document.getElementById('lightIntensity').value),
      enablePulse: document.getElementById('enablePulse').checked,
      enableGlow: document.getElementById('enableGlow').checked,
      enableChevrons: document.getElementById('enableChevrons').checked,
      enableDotMatrix: document.getElementById('enableDotMatrix').checked
    };
    
    // Apply settings
    window.enhancedSignalLogic.updateConvergenceSettings(convergenceSettings);
    window.enhancedSignalLogic.updateLightLogicSettings(lightLogicSettings);
    
    // Update interval
    const updateInterval = parseInt(document.getElementById('updateInterval').value);
    window.enhancedSignalLogic.updateInterval = updateInterval;
    
    console.log('✅ Enhanced signal logic settings applied');
  }
  
  updateStatusIndicators() {
    if (!window.enhancedSignalLogic) return;
    
    const analysis = window.enhancedSignalLogic.getConvergenceAnalysis();
    if (!analysis) return;
    
    // Update convergence strength
    const convergenceElement = document.querySelector('.convergence-strength');
    if (convergenceElement) {
      const strength = Math.round(analysis.convergenceStrength * 100);
      convergenceElement.textContent = `${strength}%`;
      convergenceElement.className = `convergence-strength ${analysis.dominantSignal}`;
    }
    
    // Update confidence level
    const confidenceElement = document.querySelector('.confidence-level');
    if (confidenceElement) {
      const confidence = Math.round(analysis.confidence * 100);
      confidenceElement.textContent = `${confidence}%`;
      confidenceElement.style.setProperty('--confidence', analysis.confidence);
    }
  }
  
  savePreset() {
    const presetName = prompt('Enter preset name:');
    if (!presetName) return;
    
    const settings = this.getCurrentSettings();
    localStorage.setItem(`signalLogicPreset_${presetName}`, JSON.stringify(settings));
    
    console.log(`💾 Preset "${presetName}" saved`);
  }
  
  getCurrentSettings() {
    return {
      convergence: {
        minConvergence: parseInt(document.getElementById('minConvergence').value),
        convergenceThreshold: parseFloat(document.getElementById('convergenceThreshold').value),
        priority: document.getElementById('signalPriority').value,
        strongSignalWeight: parseFloat(document.getElementById('strongWeight').value),
        mildSignalWeight: parseFloat(document.getElementById('mildWeight').value)
      },
      lightLogic: {
        style: document.getElementById('lightLogicStyle').value,
        intensity: parseInt(document.getElementById('lightIntensity').value),
        enablePulse: document.getElementById('enablePulse').checked,
        enableGlow: document.getElementById('enableGlow').checked,
        enableChevrons: document.getElementById('enableChevrons').checked,
        enableDotMatrix: document.getElementById('enableDotMatrix').checked
      },
      advanced: {
        updateInterval: parseInt(document.getElementById('updateInterval').value)
      }
    };
  }
  
  clearHistory() {
    if (window.enhancedSignalLogic) {
      window.enhancedSignalLogic.reset();
      console.log('🗑️ Signal logic history cleared');
    }
  }
  
  exportHistory() {
    if (!window.enhancedSignalLogic) return;
    
    const history = window.enhancedSignalLogic.convergenceHistory;
    const dataStr = JSON.stringify(history, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = `signal_logic_history_${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    
    console.log('📊 Signal logic history exported');
  }
  
  resetSettings() {
    if (confirm('Reset all signal logic settings to defaults?')) {
      // Reset to default values
      document.getElementById('minConvergence').value = 3;
      document.getElementById('convergenceThreshold').value = 0.7;
      document.getElementById('signalPriority').value = 'balanced';
      document.getElementById('strongWeight').value = 2;
      document.getElementById('mildWeight').value = 1;
      document.getElementById('lightLogicStyle').value = 'vibeflow';
      document.getElementById('lightIntensity').value = 3;
      document.getElementById('enablePulse').checked = true;
      document.getElementById('enableGlow').checked = true;
      document.getElementById('enableChevrons').checked = true;
      document.getElementById('enableDotMatrix').checked = true;
      document.getElementById('updateInterval').value = 100;
      
      this.updateSliderDisplays();
      this.applySettings();
      
      console.log('🔄 Signal logic settings reset to defaults');
    }
  }
  
  show() {
    const menuContainer = document.getElementById('logicMenu');
    if (menuContainer) {
      // 🚀 UNIFIED POSITIONING SYSTEM - MATCH OTHER MENUS
      const button = document.querySelector('[onclick*="signalLogicMenu"], #signalLogicBtn') ||
                    document.querySelector('.signal-logic-button');

      if (button) {
        const buttonRect = button.getBoundingClientRect();
        const momentumContainer = document.querySelector('.momentum-indicators');

        let leftPosition = buttonRect.left + window.scrollX;
        let topPosition = buttonRect.bottom + window.scrollY + 10;

        if (momentumContainer) {
          const momentumRect = momentumContainer.getBoundingClientRect();
          // 💥 FORCE POSITION FAR RIGHT OF MOMENTUM-INDICATORS WITH 150PX+ GAP
          leftPosition = Math.max(leftPosition + 150, momentumRect.right + 150);
          // 🎯 CENTER VERTICALLY LIKE OTHER MENUS
          topPosition = Math.max(100, window.innerHeight * 0.2);
        } else {
          leftPosition += 450; // Fallback positioning
          topPosition = Math.max(100, window.innerHeight * 0.2);
        }

        menuContainer.style.position = 'fixed';
        menuContainer.style.left = `${leftPosition}px`;
        menuContainer.style.top = `${topPosition}px`;
        menuContainer.style.zIndex = '1000';

        console.log(`[EnhancedSignalLogicMenu] 🎯 UNIFIED positioning: left ${leftPosition}, top ${topPosition}`);
      }

      menuContainer.style.display = 'block';
      this.isVisible = true;
    }
  }
  
  hide() {
    const menuContainer = document.getElementById('logicMenu');
    if (menuContainer) {
      menuContainer.style.display = 'none';
      this.isVisible = false;
    }
  }
  
  toggle() {
    if (this.isVisible) {
      this.hide();
    } else {
      this.show();
    }
  }
}

// Initialize enhanced signal logic menu
window.enhancedSignalLogicMenu = new EnhancedSignalLogicMenu();

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = EnhancedSignalLogicMenu;
}
