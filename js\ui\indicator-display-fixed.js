/* =========================================================
   StarCrypt Indicator Display System - CLEAN IMPLEMENTATION
   This implementation fixes the following issues:
   1. Prevents [object Object] in the UI
   2. Ensures exactly 7 signal lights per indicator row
   3. <PERSON><PERSON><PERSON> handles neutral state (always gray, never blank)
   4. Prevents memory leaks and duplicate event handlers
   5. Optimizes performance with controlled refresh rates
 ========================================================= */

// Global tracking for initialization
window.indicatorsInitialized = false

// Function to get proper signal color with 5-color logic
function getSignalColor(signal, strength = 0.5) {
  // Force neutral to be gray, never blank
  if (!signal || signal === 'neutral') {
    return '#808080' // Medium gray for neutral
  }

  // 5-color logic implementation
  if (signal === 'buy') {
    return strength > 0.6 ? '#00FF00' : '#00AAFF' // Strong vs mild buy
  } else if (signal === 'sell') {
    return strength > 0.6 ? '#FF0000' : '#FFA500' // Strong vs mild sell
  }

  // Fallback
  return '#808080'
}

// Create signal lights for all indicators - DISABLED
function createAllIndicatorLights() {
  console.log('[IndicatorDisplay] Signal lights creation DISABLED - using existing HTML structure')

  // The signal circles already exist in the HTML momentum-indicators table
  // They are in the correct structure: label-cell → mini-chart-cell → signals-cell
  // No need to create additional signal lights

  console.log('[IndicatorDisplay] Found existing signal circles in momentum-indicators table')

  // Just verify the existing structure
  const existingSignals = document.querySelectorAll('#momentum-indicators .signal-circle');
  console.log(`[IndicatorDisplay] Found ${existingSignals.length} existing signal circles`);
}

// Replace problematic mini-charts with enhanced ones
function replaceProblematicMiniCharts() {
  console.log('[IndicatorDisplay] Replacing problematic mini-charts with enhanced versions');

  // Find all existing mini-chart containers in momentum-indicators
  const existingCharts = document.querySelectorAll('#momentum-indicators .mini-chart-cell .mini-chart');

  existingCharts.forEach(chartContainer => {
    try {
      // Find the parent row to get the indicator name
      const row = chartContainer.closest('tr[data-indicator]');
      if (!row) return;

      const indicator = row.getAttribute('data-indicator');
      if (!indicator) return;

      console.log(`[IndicatorDisplay] Replacing mini-chart for ${indicator}`);

      // Find the mini-chart-cell container
      const miniChartCell = chartContainer.closest('.mini-chart-cell');
      if (!miniChartCell) return;

      // Create enhanced mini chart
      createEnhancedMiniChart(indicator, miniChartCell);

    } catch (error) {
      console.error('[IndicatorDisplay] Error replacing mini-chart:', error);
    }
  });
}

// Enhanced mini chart creation with proper styling - NO CHART.JS
function createEnhancedMiniChart(indicator, container) {
  try {
    // Clear existing content completely to remove Chart.js charts
    container.innerHTML = '';

    // Create wrapper with enhanced styling
    const wrapper = document.createElement('div');
    wrapper.className = 'mini-chart enhanced-mini-chart';
    wrapper.id = `mini-chart-${indicator}`;
    wrapper.style.cssText = `
      width: 100%;
      height: 60px;
      position: relative;
      overflow: hidden;
      background: linear-gradient(135deg, rgba(0, 20, 40, 0.8), rgba(0, 40, 80, 0.6));
      border: 1px solid rgba(0, 255, 255, 0.3);
      border-radius: 4px;
      box-shadow: inset 0 0 10px rgba(0, 255, 255, 0.1);
    `;

    // Create canvas for pure canvas rendering (no Chart.js)
    const canvas = document.createElement('canvas');
    canvas.id = `mini-chart-canvas-${indicator}`;
    canvas.width = 207;
    canvas.height = 58;
    canvas.style.cssText = `
      box-sizing: border-box;
      display: block;
      height: 58px;
      width: 207px;
      background: transparent;
    `;

    // Create live value display
    const valueDisplay = document.createElement('span');
    valueDisplay.className = 'live-readout';
    valueDisplay.id = `live-readout-${indicator}`;
    valueDisplay.style.cssText = `
      position: absolute;
      top: 2px;
      right: 5px;
      color: #00FFFF;
      font-size: 11px;
      font-weight: bold;
      text-shadow: 0 0 5px rgba(0, 255, 255, 0.8);
      background: rgba(0, 0, 0, 0.7);
      padding: 2px 6px;
      border-radius: 3px;
      border: 1px solid rgba(0, 255, 255, 0.3);
      z-index: 10;
    `;
    valueDisplay.textContent = '---';

    // Create grid overlay
    const gridOverlay = document.createElement('div');
    gridOverlay.className = 'grid-overlay';

    // Create indicator label
    const label = document.createElement('div');
    label.className = 'indicator-label';
    label.style.cssText = `
      position: absolute;
      bottom: 2px;
      left: 5px;
      color: rgba(255, 255, 255, 0.7);
      font-size: 9px;
      text-transform: lowercase;
      letter-spacing: 0.5px;
      z-index: 10;
    `;
    label.textContent = indicator;

    wrapper.appendChild(canvas);
    wrapper.appendChild(valueDisplay);
    wrapper.appendChild(gridOverlay);
    wrapper.appendChild(label);
    container.appendChild(wrapper);

    // Initialize pure canvas rendering (no Chart.js dependencies)
    const ctx = canvas.getContext('2d');
    if (!ctx) {
      console.error(`[IndicatorDisplay] Could not get canvas context for ${indicator}`);
      return null;
    }

    // Create chart object for pure canvas rendering
    const chart = {
      canvas,
      ctx,
      container: wrapper,
      valueDisplay,
      data: [],
      lastValue: null,
      indicator,
      render: () => renderPureCanvasChart(chart)
    };

    // Store chart reference
    if (!window.enhancedCharts) window.enhancedCharts = {};
    window.enhancedCharts[indicator] = chart;

    // Initial render with sample data
    chart.data = generateSampleData(indicator);
    chart.render();

    console.log(`[IndicatorDisplay] Created enhanced mini chart for ${indicator} (pure canvas, no Chart.js)`);
    return chart;
  } catch (error) {
    console.error(`[IndicatorDisplay] Error creating enhanced mini chart for ${indicator}:`, error);
    return null;
  }
}

// Get existing indicator row - DO NOT CREATE NEW ROWS
function getOrCreateIndicatorRow(indicator) {
  // The momentum table rows already exist in the HTML
  // We should NOT create new rows, just find existing ones

  // Look for existing row in the momentum table
  let row = document.querySelector(`#momentum-table tr[data-indicator="${indicator}"]`);

  if (!row) {
    // Also try looking in the momentum-indicators container directly
    row = document.querySelector(`#momentum-indicators tr[data-indicator="${indicator}"]`);
  }

  if (!row) {
    console.log(`[IndicatorDisplay] No existing row found for ${indicator} - this is normal, rows are created elsewhere`);
    return null;
  }

  console.log(`[IndicatorDisplay] Found existing row for ${indicator}`);
  return row;
}

// Pure canvas chart rendering (no Chart.js dependencies)
function renderPureCanvasChart(chart) {
  const { ctx, canvas, data, indicator, valueDisplay } = chart;
  const { width, height } = canvas;

  if (!data || data.length === 0) return;

  // Clear canvas
  ctx.clearRect(0, 0, width, height);

  // Calculate data range
  const values = data.map(d => d.value);
  const minVal = Math.min(...values);
  const maxVal = Math.max(...values);
  const range = Math.max(1, maxVal - minVal);

  // Add padding
  const paddedMin = minVal - range * 0.1;
  const paddedMax = maxVal + range * 0.1;
  const paddedRange = paddedMax - paddedMin;

  // Scale functions
  const scaleX = (index) => (index / (data.length - 1)) * width;
  const scaleY = (value) => height - ((value - paddedMin) / paddedRange) * height;

  // Draw grid
  ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
  ctx.lineWidth = 0.5;

  // Horizontal grid lines
  for (let i = 0; i <= 4; i++) {
    const y = (height / 4) * i;
    ctx.beginPath();
    ctx.moveTo(0, y);
    ctx.lineTo(width, y);
    ctx.stroke();
  }

  // Vertical grid lines
  for (let i = 0; i <= 6; i++) {
    const x = (width / 6) * i;
    ctx.beginPath();
    ctx.moveTo(x, 0);
    ctx.lineTo(x, height);
    ctx.stroke();
  }

  // Create gradient for line
  const gradient = ctx.createLinearGradient(0, 0, 0, height);
  const color = getIndicatorColor(indicator);
  gradient.addColorStop(0, color);
  gradient.addColorStop(1, adjustColorOpacity(color, 0.3));

  // Draw area fill
  ctx.fillStyle = gradient;
  ctx.beginPath();
  ctx.moveTo(scaleX(0), height);

  data.forEach((point, index) => {
    const x = scaleX(index);
    const y = scaleY(point.value);
    if (index === 0) {
      ctx.lineTo(x, y);
    } else {
      ctx.lineTo(x, y);
    }
  });

  ctx.lineTo(scaleX(data.length - 1), height);
  ctx.closePath();
  ctx.fill();

  // Draw line
  ctx.strokeStyle = color;
  ctx.lineWidth = 2;
  ctx.beginPath();

  data.forEach((point, index) => {
    const x = scaleX(index);
    const y = scaleY(point.value);
    if (index === 0) {
      ctx.moveTo(x, y);
    } else {
      ctx.lineTo(x, y);
    }
  });

  ctx.stroke();

  // Draw points
  ctx.fillStyle = color;
  data.forEach((point, index) => {
    const x = scaleX(index);
    const y = scaleY(point.value);
    ctx.beginPath();
    ctx.arc(x, y, 2, 0, Math.PI * 2);
    ctx.fill();
  });

  // Update live readout
  if (valueDisplay && data.length > 0) {
    const lastValue = data[data.length - 1].value;
    valueDisplay.textContent = lastValue.toFixed(2);
    valueDisplay.style.color = color;
    valueDisplay.style.textShadow = `0 0 5px ${color}`;
    valueDisplay.style.borderColor = adjustColorOpacity(color, 0.5);
  }
}

// Generate sample data for indicators
function generateSampleData(indicator) {
  const points = 20;
  const data = [];

  // Base values for different indicators
  const baseValues = {
    'rsi': 50,
    'macd': 0,
    'stoch': 50,
    'atr': 1,
    'bb': 100,
    'ema': 100,
    'sma': 100
  };

  const baseValue = baseValues[indicator] || 50;
  const volatility = indicator === 'atr' ? 0.5 : indicator === 'macd' ? 2 : 10;

  for (let i = 0; i < points; i++) {
    const trend = Math.sin(i * 0.3) * volatility * 0.5;
    const noise = (Math.random() - 0.5) * volatility;
    const value = baseValue + trend + noise;

    data.push({
      value: Math.max(0, value),
      timestamp: Date.now() - (points - i) * 60000
    });
  }

  return data;
}

// Helper function to adjust color opacity
function adjustColorOpacity(color, opacity) {
  // Convert hex to rgba
  if (color.startsWith('#')) {
    const hex = color.slice(1);
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }

  // If already rgba, adjust opacity
  if (color.startsWith('rgba')) {
    return color.replace(/[\d\.]+\)$/g, `${opacity})`);
  }

  // If rgb, convert to rgba
  if (color.startsWith('rgb')) {
    return color.replace('rgb', 'rgba').replace(')', `, ${opacity})`);
  }

  // Fallback
  return `rgba(0, 255, 255, ${opacity})`;
}

// Initialize enhanced mini charts replacement
function initEnhancedMiniCharts() {
  console.log('[IndicatorDisplay] Initializing enhanced mini charts replacement');

  // Wait for DOM to be ready
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      setTimeout(replaceProblematicMiniCharts, 1000);
    });
  } else {
    setTimeout(replaceProblematicMiniCharts, 1000);
  }
}

// Update enhanced chart with new data
function updateEnhancedChart(indicator, newData) {
  const chart = window.enhancedCharts?.[indicator];
  if (!chart) return;

  try {
    // Add new data point
    if (Array.isArray(newData)) {
      chart.data = newData;
    } else if (typeof newData === 'number') {
      chart.data.push({
        value: newData,
        timestamp: Date.now()
      });

      // Keep only last 20 points
      if (chart.data.length > 20) {
        chart.data = chart.data.slice(-20);
      }
    }

    // Re-render
    chart.render();

  } catch (error) {
    console.error(`[IndicatorDisplay] Error updating enhanced chart for ${indicator}:`, error);
  }
}

// Export functions for global access
window.initEnhancedMiniCharts = initEnhancedMiniCharts;
window.updateEnhancedChart = updateEnhancedChart;
window.replaceProblematicMiniCharts = replaceProblematicMiniCharts;

// Create mini charts for all indicators - DISABLED
function createAllMiniCharts() {
  console.log('[IndicatorDisplay] DISABLED - Enhanced charts handle chart creation now')
  // Enhanced charts handle all chart creation
}

// Enhanced chart data creation with multiple lines and professional styling
function createEnhancedChartData(indicator) {
  const timeLabels = Array.from({length: 24}, (_, i) => {
    const date = new Date()
    date.setHours(date.getHours() - (23 - i))
    return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' })
  })

  // Get indicator color
  const indicatorColor = window.getIndicatorColor ? window.getIndicatorColor(indicator) : '#00FFFF'

  // Create gradient colors
  const primaryColor = indicatorColor
  const secondaryColor = adjustColorOpacity(primaryColor, 0.6)
  const tertiaryColor = adjustColorOpacity(primaryColor, 0.3)

  // Base dataset configuration
  const baseDataset = {
    borderWidth: 2,
    pointRadius: 0,
    pointHoverRadius: 4,
    pointHoverBorderWidth: 2,
    tension: 0.4, // Smooth curves
    fill: true,
  }

  // Create datasets based on indicator type
  if (indicator === 'macd') {
    return {
      labels: timeLabels,
      datasets: [
        {
          ...baseDataset,
          label: 'MACD Line',
          data: Array(24).fill(0).map(() => (Math.random() - 0.5) * 2),
          borderColor: primaryColor,
          backgroundColor: createGradient(primaryColor, 0.3),
          yAxisID: 'y',
        },
        {
          ...baseDataset,
          label: 'Signal Line',
          data: Array(24).fill(0).map(() => (Math.random() - 0.5) * 1.5),
          borderColor: secondaryColor,
          backgroundColor: createGradient(secondaryColor, 0.2),
          yAxisID: 'y',
        },
        {
          ...baseDataset,
          label: 'Histogram',
          data: Array(24).fill(0).map(() => (Math.random() - 0.5) * 0.5),
          borderColor: tertiaryColor,
          backgroundColor: createGradient(tertiaryColor, 0.4),
          type: 'bar',
          yAxisID: 'y',
        }
      ]
    }
  } else if (indicator === 'bollingerBands') {
    return {
      labels: timeLabels,
      datasets: [
        {
          ...baseDataset,
          label: 'Upper Band',
          data: Array(24).fill(0).map(() => 80 + Math.random() * 15),
          borderColor: adjustColorOpacity(primaryColor, 0.8),
          backgroundColor: 'transparent',
          borderDash: [5, 5],
        },
        {
          ...baseDataset,
          label: 'Middle Band (SMA)',
          data: Array(24).fill(0).map(() => 50 + (Math.random() - 0.5) * 20),
          borderColor: primaryColor,
          backgroundColor: createGradient(primaryColor, 0.2),
        },
        {
          ...baseDataset,
          label: 'Lower Band',
          data: Array(24).fill(0).map(() => 20 + Math.random() * 15),
          borderColor: adjustColorOpacity(primaryColor, 0.8),
          backgroundColor: 'transparent',
          borderDash: [5, 5],
        }
      ]
    }
  } else if (['rsi', 'stochRsi', 'mfi', 'williamsR'].includes(indicator)) {
    return {
      labels: timeLabels,
      datasets: [
        {
          ...baseDataset,
          label: indicator.toUpperCase(),
          data: Array(24).fill(0).map(() => 30 + Math.random() * 40),
          borderColor: primaryColor,
          backgroundColor: createGradient(primaryColor, 0.3),
        },
        {
          ...baseDataset,
          label: 'Overbought (70)',
          data: Array(24).fill(70),
          borderColor: 'rgba(255, 0, 0, 0.5)',
          backgroundColor: 'transparent',
          borderDash: [3, 3],
          pointRadius: 0,
          pointHoverRadius: 0,
        },
        {
          ...baseDataset,
          label: 'Oversold (30)',
          data: Array(24).fill(30),
          borderColor: 'rgba(0, 255, 0, 0.5)',
          backgroundColor: 'transparent',
          borderDash: [3, 3],
          pointRadius: 0,
          pointHoverRadius: 0,
        }
      ]
    }
  } else {
    // Default single line with enhanced styling
    return {
      labels: timeLabels,
      datasets: [{
        ...baseDataset,
        label: indicator.toUpperCase(),
        data: Array(24).fill(0).map(() => 30 + Math.random() * 40),
        borderColor: primaryColor,
        backgroundColor: createGradient(primaryColor, 0.3),
      }]
    }
  }
}

// Enhanced chart options with professional styling
function createEnhancedChartOptions(indicator) {
  return {
    responsive: true,
    maintainAspectRatio: false,
    animation: {
      duration: 750,
      easing: 'easeInOutQuart'
    },
    interaction: {
      intersect: false,
      mode: 'index'
    },
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        enabled: true,
        backgroundColor: 'rgba(0, 10, 20, 0.95)',
        titleColor: '#00FFFF',
        bodyColor: '#FFFFFF',
        borderColor: '#00FFFF',
        borderWidth: 1,
        cornerRadius: 8,
        displayColors: true,
        callbacks: {
          title: function(context) {
            return `${indicator.toUpperCase()} - ${context[0].label}`
          },
          label: function(context) {
            const value = typeof context.parsed.y === 'number' ? context.parsed.y.toFixed(2) : 'N/A'
            return `${context.dataset.label}: ${value}`
          }
        }
      }
    },
    scales: {
      x: {
        display: false,
        grid: {
          display: false
        }
      },
      y: {
        display: false,
        grid: {
          display: false
        },
        min: getYAxisMin(indicator),
        max: getYAxisMax(indicator),
      }
    },
    elements: {
      point: {
        hoverBackgroundColor: '#00FFFF',
        hoverBorderColor: '#FFFFFF'
      }
    }
  }
}

// Helper function to create gradient
function createGradient(color, opacity) {
  // This will be replaced with actual gradient when canvas context is available
  return adjustColorOpacity(color, opacity)
}

// Helper function to adjust color opacity
function adjustColorOpacity(color, opacity) {
  // Convert hex to rgba
  if (color.startsWith('#')) {
    const r = parseInt(color.slice(1, 3), 16)
    const g = parseInt(color.slice(3, 5), 16)
    const b = parseInt(color.slice(5, 7), 16)
    return `rgba(${r}, ${g}, ${b}, ${opacity})`
  }
  return color
}

// Helper function to get Y-axis minimum
function getYAxisMin(indicator) {
  if (['rsi', 'stochRsi', 'mfi'].includes(indicator)) return 0
  if (indicator === 'williamsR') return -100
  if (indicator === 'adx') return 0
  return undefined // Auto-scale
}

// Helper function to get Y-axis maximum
function getYAxisMax(indicator) {
  if (['rsi', 'stochRsi', 'mfi', 'adx'].includes(indicator)) return 100
  if (indicator === 'williamsR') return 0
  return undefined // Auto-scale
}

// Create mini chart for a specific indicator
function createMiniChart(indicator) {
  try {
    // Find the chart container
    const chartContainer = document.getElementById(`${indicator}-chart-container`)
    if (!chartContainer) {
      console.warn(`[IndicatorDisplay] Chart container not found for ${indicator}`)
      return
    }

    // Clear any existing content
    chartContainer.innerHTML = ''

    // Create canvas element
    const canvas = document.createElement('canvas')
    canvas.id = `${indicator}-chart`
    canvas.width = 120
    canvas.height = 40
    chartContainer.appendChild(canvas)

    // Get canvas context
    const ctx = canvas.getContext('2d')
    if (!ctx) {
      console.error(`[IndicatorDisplay] Could not get context for ${indicator} chart`)
      return
    }

    // Enhanced professional chart data with multiple lines and gradients
    const data = createEnhancedChartData(indicator)

    // Create actual gradients for the canvas
    data.datasets.forEach(dataset => {
      if (dataset.backgroundColor && typeof dataset.backgroundColor === 'string' && dataset.backgroundColor.includes('rgba')) {
        const gradient = ctx.createLinearGradient(0, 0, 0, 40)
        const color = dataset.backgroundColor
        gradient.addColorStop(0, color)
        gradient.addColorStop(1, 'rgba(0, 0, 0, 0)')
        dataset.backgroundColor = gradient
      }
    })

    // Enhanced professional chart options
    const options = createEnhancedChartOptions(indicator)

    // DISABLED - Enhanced charts handle chart creation now
    console.log(`[IndicatorDisplay] DISABLED - Chart creation for ${indicator} handled by enhanced charts`)
  } catch (error) {
    console.error(`[IndicatorDisplay] Error creating chart for ${indicator}:`, error)
    // Create fallback visualization when chart fails
    createFallbackVisualization(indicator)
  }
}

// Create fallback visualization when chart data is N/A or invalid
function createFallbackVisualization(indicator) {
  try {
    const chartContainer = document.getElementById(`${indicator}-chart-container`)
    if (!chartContainer) return

    // Clear container
    chartContainer.innerHTML = ''

    // Create fallback visual element
    const fallbackDiv = document.createElement('div')
    fallbackDiv.className = 'chart-fallback'
    fallbackDiv.style.cssText = `
      width: 100%;
      height: 40px;
      background: linear-gradient(45deg,
        rgba(0, 255, 255, 0.1) 0%,
        rgba(0, 255, 255, 0.05) 50%,
        rgba(0, 255, 255, 0.1) 100%);
      border: 1px solid rgba(0, 255, 255, 0.3);
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      overflow: hidden;
    `

    // Add animated bars to simulate activity
    for (let i = 0; i < 8; i++) {
      const bar = document.createElement('div')
      bar.style.cssText = `
        width: 2px;
        height: ${10 + Math.random() * 20}px;
        background: rgba(0, 255, 255, 0.6);
        margin: 0 2px;
        animation: pulse ${1 + Math.random()}s ease-in-out infinite alternate;
        animation-delay: ${i * 0.1}s;
      `
      fallbackDiv.appendChild(bar)
    }

    // Add status text
    const statusText = document.createElement('div')
    statusText.textContent = 'Awaiting Data'
    statusText.style.cssText = `
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      color: rgba(0, 255, 255, 0.8);
      font-size: 8px;
      font-family: 'Orbitron', monospace;
      text-shadow: 0 0 4px rgba(0, 255, 255, 0.5);
      pointer-events: none;
    `
    fallbackDiv.appendChild(statusText)

    chartContainer.appendChild(fallbackDiv)

    // Add CSS animation if not already present
    if (!document.getElementById('fallback-animations')) {
      const style = document.createElement('style')
      style.id = 'fallback-animations'
      style.textContent = `
        @keyframes pulse {
          0% { opacity: 0.3; transform: scaleY(0.5); }
          100% { opacity: 1; transform: scaleY(1); }
        }
      `
      document.head.appendChild(style)
    }

  } catch (error) {
    console.error(`[IndicatorDisplay] Error creating fallback visualization for ${indicator}:`, error)
  }
}

// Update all indicators - DISABLED
function updateAllIndicators() {
  try {
    // DISABLED - Enhanced charts handle all updates now
    console.log('[IndicatorDisplay] DISABLED - All indicator updates handled by enhanced charts')
    return

    // Get indicators and timeframes
    const indicators = getAllRequiredIndicators()
    const timeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w']

    // Start with current timeframe for immediate feedback
    const currentTf = window.currentTf || '1h'
    indicators.forEach(indicator => {
      updateIndicator(indicator, currentTf)
    })

    // Then update other timeframes with delays
    let delay = 100
    timeframes.forEach(tf => {
      if (tf === currentTf) return // Skip current timeframe

      setTimeout(() => {
        indicators.forEach(indicator => {
          updateIndicator(indicator, tf)
        })
      }, delay)
      delay += 50
    })
  } catch (error) {
    console.error('[IndicatorDisplay] Error updating indicators:', error)
  }
}

// Update a specific indicator for a specific timeframe
function updateIndicator(indicator, timeframe) {
  try {
    // Get signal data
    const data = getIndicatorData(indicator, timeframe)
    if (!data) return

    // Update signal light
    updateSignalLight(indicator, timeframe, data)

    // Update chart if it's the current timeframe
    if (timeframe === window.currentTf) {
      updateChart(indicator, data)
    }
  } catch (error) {
    console.error(`[IndicatorDisplay] Error updating ${indicator} for ${timeframe}:`, error)
  }
}

// Get indicator data based on server response or simulation
function getIndicatorData(indicator, timeframe) {
  // Try to use real server data if available
  if (window.indicatorData &&
      window.indicatorData[timeframe] &&
      window.indicatorData[timeframe][indicator]) {
    return window.indicatorData[timeframe][indicator]
  }

  // Otherwise generate simulated data
  return {
    value: 50 + (Math.random() * 50 - 25), // 25-75 range
    signal: Math.random() > 0.7 ? (Math.random() > 0.5 ? 'buy' : 'sell') : 'neutral',
    strength: Math.random(),
    change: Math.random() * 10 - 5,
  }
}

// Update signal light for an indicator - ensure compatibility with update-signal-lights.js
function updateSignalLight(indicator, timeframe, data) {
  // Find the signal light using selector that matches what update-signal-lights.js expects
  const light = document.querySelector(`.signal-circle[data-ind="${indicator}"][data-tf="${timeframe}"]`) ||
                document.getElementById(`${indicator}-${timeframe}-signal`)
  if (!light) return

  // Make sure the light has the correct classes and data attributes
  light.classList.add('signal-circle') // Ensure it has signal-circle class
  light.setAttribute('data-ind', indicator) // Critical for updates
  light.setAttribute('data-tf', timeframe) // Critical for updates

  // Calculate signal color
  const color = getSignalColor(data.signal, data.strength)

  // Set the appropriate color class
  light.classList.remove('green-light', 'blue-light', 'orange-light', 'red-light', 'grey-light')

  // Add the appropriate color class based on signal
  if (data.signal === 'buy' && data.strength > 0.6) {
    light.classList.add('green-light')
  } else if (data.signal === 'buy') {
    light.classList.add('blue-light')
  } else if (data.signal === 'sell' && data.strength > 0.6) {
    light.classList.add('red-light')
  } else if (data.signal === 'sell') {
    light.classList.add('orange-light')
  } else {
    light.classList.add('grey-light')
  }

  // Apply color as inline style as well for backwards compatibility
  light.style.backgroundColor = color || '#808080'

  // Safely format the value for display
  let displayValue = 'N/A'
  try {
    const numValue = parseFloat(data.value)
    displayValue = !isNaN(numValue) ? numValue.toFixed(1) : 'N/A'
  } catch (e) {
    console.warn(`Error formatting value for ${indicator}:`, data.value, e)
  }

  // Update tooltip with safe value
  const tooltipText = `${timeframe.toUpperCase()} ${indicator.toUpperCase()}: ${data.signal || 'N/A'} (${displayValue})`
  light.setAttribute('data-tooltip', tooltipText)
  light.title = tooltipText

  // Add pulse effect for strong signals
  if (data.strength > 0.7) {
    light.classList.add('pulse')
  } else {
    light.classList.remove('pulse')
  }
}

// Enhanced chart update function for professional multi-line charts
function updateChart(indicator, data) {
  // DISABLED - Enhanced charts handle all updates now
  console.log(`[IndicatorDisplay] DISABLED - Chart updates for ${indicator} handled by enhanced charts`)
  return
}

// Update MACD chart with multiple lines
function updateMACDChart(chart, data) {
  const macdValue = data.macd || data.value || 0
  const signalValue = data.signal || data.signalLine || 0
  const histogram = data.histogram || (macdValue - signalValue)

  // Update MACD line (dataset 0)
  if (chart.data.datasets[0]) {
    chart.data.datasets[0].data.push(macdValue)
    chart.data.datasets[0].data.shift()
  }

  // Update Signal line (dataset 1)
  if (chart.data.datasets[1]) {
    chart.data.datasets[1].data.push(signalValue)
    chart.data.datasets[1].data.shift()
  }

  // Update Histogram (dataset 2)
  if (chart.data.datasets[2]) {
    chart.data.datasets[2].data.push(histogram)
    chart.data.datasets[2].data.shift()
  }
}

// Update Bollinger Bands chart
function updateBollingerBandsChart(chart, data) {
  const upper = data.upper || 0
  const middle = data.middle || data.sma || data.value || 0
  const lower = data.lower || 0

  // Update Upper Band (dataset 0)
  if (chart.data.datasets[0]) {
    chart.data.datasets[0].data.push(upper)
    chart.data.datasets[0].data.shift()
  }

  // Update Middle Band (dataset 1)
  if (chart.data.datasets[1]) {
    chart.data.datasets[1].data.push(middle)
    chart.data.datasets[1].data.shift()
  }

  // Update Lower Band (dataset 2)
  if (chart.data.datasets[2]) {
    chart.data.datasets[2].data.push(lower)
    chart.data.datasets[2].data.shift()
  }
}

// Update oscillator charts (RSI, Stoch RSI, etc.)
function updateOscillatorChart(chart, data, indicator) {
  const value = data.value || 50

  // Update main oscillator line (dataset 0)
  if (chart.data.datasets[0]) {
    chart.data.datasets[0].data.push(value)
    chart.data.datasets[0].data.shift()

    // Update color based on signal strength
    const color = getEnhancedSignalColor(data.signal, data.strength, indicator)
    chart.data.datasets[0].borderColor = color.border
    chart.data.datasets[0].backgroundColor = color.background
  }

  // Overbought and oversold lines (datasets 1 and 2) remain static
}

// Update generic single-line charts
function updateGenericChart(chart, data, indicator) {
  const value = data.value || 50

  // Update main line (dataset 0)
  if (chart.data.datasets[0]) {
    chart.data.datasets[0].data.push(value)
    chart.data.datasets[0].data.shift()

    // Update color based on signal
    const color = getEnhancedSignalColor(data.signal, data.strength, indicator)
    chart.data.datasets[0].borderColor = color.border
    chart.data.datasets[0].backgroundColor = color.background
  }
}

// Get enhanced signal colors for charts
function getEnhancedSignalColor(signal, strength, indicator) {
  const baseColor = window.getIndicatorColor ? window.getIndicatorColor(indicator) : '#00FFFF'

  if (signal === 'buy') {
    const intensity = strength > 0.7 ? 1 : 0.7
    return {
      border: `rgba(0, 255, 0, ${intensity})`,
      background: `rgba(0, 255, 0, ${intensity * 0.3})`
    }
  } else if (signal === 'sell') {
    const intensity = strength > 0.7 ? 1 : 0.7
    return {
      border: `rgba(255, 0, 0, ${intensity})`,
      background: `rgba(255, 0, 0, ${intensity * 0.3})`
    }
  } else {
    return {
      border: baseColor,
      background: adjustColorOpacity(baseColor, 0.3)
    }
  }
}

// Get all required indicators for current strategy
function getAllRequiredIndicators() {
  try {
    // Get current strategy
    const strategy = window.currentStrategy || 'admiral_toa'

    // Get strategy details
    const strategyDetails = window.TRADING_STRATEGIES && window.TRADING_STRATEGIES[strategy]
    if (!strategyDetails) {
      console.warn(`[IndicatorDisplay] Strategy not found: ${strategy}`)
      return window.enabledIndicators || []
    }

    // Return strategy indicators or fallback to default enabled indicators
    return strategyDetails.indicators || window.enabledIndicators || []
  } catch (error) {
    console.error('[IndicatorDisplay] Error getting required indicators:', error)
    return []
  }
}

// Cleanup existing intervals to prevent memory leaks
function cleanupExistingHandlers() {
  // Clear any existing indicator update intervals
  if (window.indicatorUpdateInterval) {
    clearInterval(window.indicatorUpdateInterval)
    window.indicatorUpdateInterval = null
  }

  // Clear any existing chart instances
  if (window.indicatorCharts) {
    Object.values(window.indicatorCharts).forEach(chart => {
      try {
        chart.destroy()
      } catch (e) {
        // Ignore errors during cleanup
      }
    })
    window.indicatorCharts = {}
  }
}

// Initialize the indicator display system - COMPLETELY DISABLED
function initializeIndicatorDisplay() {
  console.log('[IndicatorDisplay] 🚫 COMPLETELY DISABLED - Enhanced Mini-Charts system has full control')

  // Clear any existing intervals
  if (window.indicatorUpdateInterval) {
    clearInterval(window.indicatorUpdateInterval)
    window.indicatorUpdateInterval = null
  }

  // Mark as disabled to prevent any future initialization
  window.indicatorDisplayDisabled = true
  return
}

// DISABLED - Enhanced Mini-Charts system has full control
// document.addEventListener('DOMContentLoaded', initializeIndicatorDisplay)

// Export disabled functions that redirect to enhanced charts
window.initializeIndicatorDisplay = () => {
  console.log('[IndicatorDisplay] 🚫 DISABLED - Redirected to Enhanced Mini-Charts')
}
window.updateAllIndicators = () => {
  console.log('[IndicatorDisplay] 🚫 DISABLED - Enhanced Mini-Charts handle updates')
}
window.createAllIndicatorLights = () => {
  console.log('[IndicatorDisplay] 🚫 DISABLED - Enhanced Mini-Charts handle creation')
}
window.createAllMiniCharts = createAllMiniCharts
window.replaceProblematicMiniCharts = replaceProblematicMiniCharts
window.updateEnhancedChart = updateEnhancedChart
