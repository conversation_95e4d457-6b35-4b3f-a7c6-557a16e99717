/**
 * StarCrypt Starfield Animation
 * Futuristic light-speed star field background animation
 * Reactive to ML buy/sell signals with comets/meteors
 */

class StarfieldAnimation {
  constructor() {
    this.canvas = null;
    this.ctx = null;
    this.stars = [];
    this.comets = [];
    this.meteors = [];
    this.animationId = null;
    this.isRunning = false;
    
    // Configuration - Enhanced for maximum degenism
    this.config = {
      starCount: 300,
      maxSpeed: 15,
      minSpeed: 0.8,
      starColors: ['#FFFFFF', '#00FFFF', '#0088FF', '#FFD700', '#FF6B6B', '#00FF00', '#FF00FF', '#FFAA00'],
      cometColors: {
        buy: ['#00FF00', '#00FFAA', '#AAFFAA', '#00FF88', '#88FFAA'],
        sell: ['#FF0000', '#FF6666', '#FFAAAA', '#FF4444', '#FF8888'],
        neutral: ['#00FFFF', '#AAFFFF', '#FFFFFF', '#88AAFF', '#CCDDFF']
      },
      meteorFrequency: 0.03, // Increased frequency for more action
      cometFrequency: 0.015,
      trailLength: 30,
      glowIntensity: 1.2,
      warpSpeed: false,
      hyperSpace: false,
      quantumBurst: false,
      degenMode: true // Maximum visual chaos
    };
    
    this.mlSignalState = 'neutral'; // 'buy', 'sell', 'neutral'
    this.signalIntensity = 0.5; // 0-1 scale
    
    this.init();
  }
  
  init() {
    console.log('[StarfieldAnimation] 🌟 Initializing starfield animation...');

    try {
      this.createCanvas();
      this.generateStars();
      this.bindEvents();
      this.setupStrategyChangeListener();
      this.start();

      console.log('[StarfieldAnimation] ✅ Starfield animation initialized successfully');
      console.log('[StarfieldAnimation] 🎯 Canvas created:', !!this.canvas);
      console.log('[StarfieldAnimation] 🎯 Stars generated:', this.stars.length);
      console.log('[StarfieldAnimation] 🎯 Animation running:', this.isRunning);
    } catch (error) {
      console.error('[StarfieldAnimation] Error initializing starfield:', error);
    }
  }

  setupStrategyChangeListener() {
    // 🛡️ PROTECTED STRATEGY CHANGE LISTENER - PREVENT WHITE SCREEN
    document.addEventListener('strategyChanged', (event) => {
      try {
        console.log('[StarfieldAnimation] 🚀 Strategy changed - triggering SAFE warp jump!');

        // 🚫 PREVENT WARP DURING CRITICAL UI UPDATES
        if (this.isUIUpdating) {
          console.log('[StarfieldAnimation] ⚠️ UI updating - delaying warp jump');
          setTimeout(() => this.triggerSafeWarpJump(), 1000);
          return;
        }

        // 🛡️ SAFE WARP TRIGGER WITH BACKGROUND PROTECTION
        this.triggerSafeWarpJump();
      } catch (error) {
        console.error('[StarfieldAnimation] ❌ Error in strategy change handler:', error);
        // Don't let animation errors break the UI
      }
    });

    // Also listen for custom warp events
    document.addEventListener('triggerWarpJump', () => {
      this.triggerSafeWarpJump();
    });
  }

  // 🛡️ SAFE WARP JUMP - PROTECTS UI FROM WHITE SCREEN
  triggerSafeWarpJump() {
    try {
      // Mark UI as updating to prevent conflicts
      this.isUIUpdating = true;

      // Trigger warp with protection
      this.triggerWarpJump();

      // Clear UI updating flag after warp completes
      setTimeout(() => {
        this.isUIUpdating = false;
      }, 5000);
    } catch (error) {
      console.error('[StarfieldAnimation] ❌ Error in safe warp jump:', error);
      this.isUIUpdating = false;
    }
  }

  triggerWarpJump() {
    console.log('[StarfieldAnimation] 🚀 WARP JUMP ACTIVATED! ENGAGING HYPERDRIVE!');

    // Enable all warp effects
    this.config.warpSpeed = true;
    this.config.hyperSpace = true;
    this.config.quantumBurst = true;
    this.config.degenMode = true;

    // Create dramatic screen flash effect
    this.createWarpFlash();

    // Increase star speed dramatically for warp effect
    this.stars.forEach(star => {
      star.originalSpeed = star.speed;
      star.speed *= 8; // Even more dramatic
      star.warpTrail = [];
      star.warpIntensity = 1.0;
      // Add random warp direction variation
      star.warpAngle = Math.random() * Math.PI * 2;
    });

    // Create massive burst of warp comets
    for (let i = 0; i < 50; i++) {
      this.comets.push(this.createWarpComet());
    }

    // Create quantum burst meteors
    for (let i = 0; i < 30; i++) {
      this.meteors.push(this.createQuantumMeteor());
    }

    // Add screen shake effect
    this.addScreenShake();

    // Create warp tunnel effect
    this.createWarpTunnel();

    // 🛡️ PROTECTED WARP RESET - ENSURE BACKGROUND ANIMATION CONTINUES
    setTimeout(() => {
      console.log('[StarfieldAnimation] 🌟 Warp jump complete - safely returning to normal space');

      try {
        // Gradually reduce warp effects to prevent jarring transition
        this.config.warpSpeed = false;
        this.config.hyperSpace = false;
        this.config.quantumBurst = false;

        // 🔧 SAFE STAR SPEED RESTORATION
        this.stars.forEach(star => {
          if (star.originalSpeed) {
            star.speed = star.originalSpeed;
            delete star.originalSpeed;
          } else {
            // Fallback: ensure reasonable speed
            star.speed = Math.max(0.5, star.speed / 8);
          }

          // Clear warp-specific properties
          star.warpTrail = [];
          star.warpIntensity = 0;
          delete star.warpAngle;
        });

        // 🧹 CLEAN UP WARP EFFECTS SAFELY
        if (this.comets) {
          this.comets = this.comets.filter(comet => !comet.isWarpComet);
        }
        if (this.meteors) {
          this.meteors = this.meteors.filter(meteor => !meteor.isQuantumMeteor);
        }
        if (this.warpRings) {
          this.warpRings = [];
        }

        console.log('[StarfieldAnimation] ✅ Background animation restored successfully');
      } catch (error) {
        console.error('[StarfieldAnimation] ❌ Error during warp reset:', error);
        // Force restart animation if reset fails
        this.init();
      }
    }, 4000); // Longer warp duration for more impact
  }

  createWarpFlash() {
    // Create a bright flash effect
    if (this.ctx && this.canvas) {
      this.ctx.save();
      this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
      this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
      this.ctx.restore();

      // Fade out the flash
      setTimeout(() => {
        if (this.ctx && this.canvas) {
          this.ctx.save();
          this.ctx.fillStyle = 'rgba(255, 255, 255, 0.4)';
          this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
          this.ctx.restore();
        }
      }, 100);
    }
  }

  createWarpComet() {
    return {
      x: Math.random() * this.canvas.width,
      y: Math.random() * this.canvas.height,
      speed: 15 + Math.random() * 25,
      angle: Math.random() * Math.PI * 2,
      color: this.config.cometColors.neutral[Math.floor(Math.random() * this.config.cometColors.neutral.length)],
      trail: [],
      life: 1.0,
      size: 3 + Math.random() * 4,
      isWarpComet: true,
      intensity: 1.5
    };
  }

  createQuantumMeteor() {
    return {
      x: Math.random() * this.canvas.width,
      y: Math.random() * this.canvas.height,
      vx: (Math.random() - 0.5) * 30,
      vy: (Math.random() - 0.5) * 30,
      color: ['#FF00FF', '#00FFFF', '#FFFF00', '#FF0080'][Math.floor(Math.random() * 4)],
      trail: [],
      life: 1.0,
      size: 2 + Math.random() * 3,
      isQuantumMeteor: true,
      quantum: true
    };
  }

  addScreenShake() {
    // Add subtle screen shake effect by modifying canvas transform
    let shakeIntensity = 5;
    let shakeCount = 0;
    const maxShakes = 20;

    const shake = () => {
      if (shakeCount < maxShakes && this.canvas) {
        const offsetX = (Math.random() - 0.5) * shakeIntensity;
        const offsetY = (Math.random() - 0.5) * shakeIntensity;

        this.canvas.style.transform = `translate(${offsetX}px, ${offsetY}px)`;

        shakeIntensity *= 0.9; // Reduce shake over time
        shakeCount++;

        setTimeout(shake, 50);
      } else if (this.canvas) {
        this.canvas.style.transform = 'translate(0px, 0px)';
      }
    };

    shake();
  }

  createWarpTunnel() {
    // Create expanding rings for tunnel effect
    this.warpRings = [];
    for (let i = 0; i < 5; i++) {
      setTimeout(() => {
        this.warpRings.push({
          x: this.canvas.width / 2,
          y: this.canvas.height / 2,
          radius: 0,
          maxRadius: Math.max(this.canvas.width, this.canvas.height),
          life: 1.0,
          color: `rgba(0, 255, 255, ${0.3 - i * 0.05})`
        });
      }, i * 200);
    }
  }
  
  createCanvas() {
    console.log('[StarfieldAnimation] 🎯 Setting up starfield canvas...');

    // Try to use existing starfield canvas first
    this.canvas = document.getElementById('starfield');

    if (!this.canvas) {
      console.log('[StarfieldAnimation] Creating new starfield canvas...');
      // Create canvas element if it doesn't exist
      this.canvas = document.createElement('canvas');
      this.canvas.id = 'starfield';
      this.canvas.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        pointer-events: none;
        background: radial-gradient(ellipse at center, #0a0a1a 0%, #000000 100%);
      `;

      // Insert as first child of body to be behind everything
      document.body.insertBefore(this.canvas, document.body.firstChild);
    } else {
      console.log('[StarfieldAnimation] ✅ Using existing starfield canvas');
      // Ensure proper styling for existing canvas
      this.canvas.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: -1;
        pointer-events: none;
        background: radial-gradient(ellipse at center, #0a0a1a 0%, #000000 100%);
      `;
    }

    this.ctx = this.canvas.getContext('2d');
    if (!this.ctx) {
      console.error('[StarfieldAnimation] ❌ Failed to get canvas context!');
      return;
    }

    this.resizeCanvas();
    console.log('[StarfieldAnimation] ✅ Canvas setup complete:', this.canvas.width, 'x', this.canvas.height);
  }
  
  resizeCanvas() {
    this.canvas.width = window.innerWidth;
    this.canvas.height = window.innerHeight;
  }
  
  generateStars() {
    this.stars = [];
    for (let i = 0; i < this.config.starCount; i++) {
      this.stars.push({
        x: Math.random() * this.canvas.width,
        y: Math.random() * this.canvas.height,
        z: Math.random() * 1000,
        speed: Math.random() * (this.config.maxSpeed - this.config.minSpeed) + this.config.minSpeed,
        color: this.config.starColors[Math.floor(Math.random() * this.config.starColors.length)],
        brightness: Math.random() * 0.8 + 0.2,
        twinkle: Math.random() * Math.PI * 2
      });
    }
  }
  
  createComet(type = 'neutral') {
    const colors = this.config.cometColors[type];
    return {
      x: Math.random() * this.canvas.width,
      y: -50,
      vx: (Math.random() - 0.5) * 4,
      vy: Math.random() * 8 + 5,
      size: Math.random() * 3 + 2,
      color: colors[Math.floor(Math.random() * colors.length)],
      trail: [],
      life: 1.0,
      type: type
    };
  }
  
  createMeteor() {
    return {
      x: Math.random() * this.canvas.width,
      y: -50,
      vx: (Math.random() - 0.5) * 6,
      vy: Math.random() * 12 + 8,
      size: Math.random() * 5 + 3,
      color: '#FFD700',
      trail: [],
      life: 1.0,
      sparkles: []
    };
  }
  
  updateStars() {
    const centerX = this.canvas.width / 2;
    const centerY = this.canvas.height / 2;

    // Enhanced speed calculation with warp effects
    const baseSpeedMultiplier = 1 + this.signalIntensity * 2;
    const warpMultiplier = this.config.warpSpeed ? 5 : 1;
    const hyperMultiplier = this.config.hyperSpace ? 10 : 1;

    this.stars.forEach(star => {
      // Enhanced movement with quantum effects
      let speedMultiplier = baseSpeedMultiplier * warpMultiplier * hyperMultiplier;

      // Quantum burst effect - random speed spikes
      if (this.config.quantumBurst && Math.random() < 0.05) {
        speedMultiplier *= 3;
        star.quantumGlow = 1.0;
      }

      // Decay quantum glow
      if (star.quantumGlow) {
        star.quantumGlow *= 0.95;
        if (star.quantumGlow < 0.1) star.quantumGlow = 0;
      }

      // Move star towards viewer (z decreases)
      star.z -= star.speed * speedMultiplier;

      // Reset star when it gets too close
      if (star.z <= 0) {
        star.z = 1000 + Math.random() * 500; // Randomize depth
        star.x = Math.random() * this.canvas.width;
        star.y = Math.random() * this.canvas.height;
        // Randomize color on reset for degen effect
        if (this.config.degenMode) {
          star.color = this.config.starColors[Math.floor(Math.random() * this.config.starColors.length)];
        }
      }

      // Enhanced twinkle with signal intensity
      star.twinkle += 0.1 + this.signalIntensity * 0.2;
    });
  }
  
  updateComets() {
    // Create new comets based on ML signals
    if (Math.random() < this.config.cometFrequency * (1 + this.signalIntensity)) {
      this.comets.push(this.createComet(this.mlSignalState));
    }
    
    // Update existing comets
    this.comets = this.comets.filter(comet => {
      comet.x += comet.vx;
      comet.y += comet.vy;
      comet.life -= 0.01;
      
      // Add to trail
      comet.trail.push({ x: comet.x, y: comet.y, life: comet.life });
      if (comet.trail.length > this.config.trailLength) {
        comet.trail.shift();
      }
      
      return comet.life > 0 && comet.y < this.canvas.height + 100;
    });
  }
  
  updateMeteors() {
    // Create new meteors
    if (Math.random() < this.config.meteorFrequency) {
      this.meteors.push(this.createMeteor());
    }
    
    // Update existing meteors
    this.meteors = this.meteors.filter(meteor => {
      meteor.x += meteor.vx;
      meteor.y += meteor.vy;
      meteor.life -= 0.008;
      
      // Add to trail
      meteor.trail.push({ x: meteor.x, y: meteor.y, life: meteor.life });
      if (meteor.trail.length > this.config.trailLength * 1.5) {
        meteor.trail.shift();
      }
      
      // Add sparkles
      if (Math.random() < 0.3) {
        meteor.sparkles.push({
          x: meteor.x + (Math.random() - 0.5) * 10,
          y: meteor.y + (Math.random() - 0.5) * 10,
          life: 0.5,
          size: Math.random() * 2 + 1
        });
      }
      
      // Update sparkles
      meteor.sparkles = meteor.sparkles.filter(sparkle => {
        sparkle.life -= 0.05;
        return sparkle.life > 0;
      });
      
      return meteor.life > 0 && meteor.y < this.canvas.height + 100;
    });
  }
  
  drawStars() {
    const centerX = this.canvas.width / 2;
    const centerY = this.canvas.height / 2;

    this.stars.forEach(star => {
      // Calculate 3D projection
      const x = (star.x - centerX) * (1000 / star.z) + centerX;
      const y = (star.y - centerY) * (1000 / star.z) + centerY;
      let size = (1000 / star.z) * 2;

      // Skip if outside canvas
      if (x < 0 || x > this.canvas.width || y < 0 || y > this.canvas.height) return;

      // Enhanced size calculation for warp effects
      if (this.config.warpSpeed) {
        size *= 1.5;
      }
      if (this.config.hyperSpace) {
        size *= 2;
      }

      // Calculate brightness with enhanced twinkle effect
      const twinkleBrightness = (Math.sin(star.twinkle) + 1) * 0.5;
      let brightness = star.brightness * twinkleBrightness * (1000 / star.z) * 0.5;

      // Quantum glow enhancement
      if (star.quantumGlow) {
        brightness += star.quantumGlow;
        size += star.quantumGlow * 3;
      }

      // Signal intensity enhancement
      brightness *= (1 + this.signalIntensity * 0.5);

      // Draw star with enhanced glow
      this.ctx.save();
      this.ctx.globalAlpha = Math.min(brightness, 1);
      this.ctx.fillStyle = star.color;
      this.ctx.shadowColor = star.color;
      this.ctx.shadowBlur = size * this.config.glowIntensity * (1 + this.signalIntensity);

      // Draw multiple layers for degen effect
      if (this.config.degenMode && brightness > 0.7) {
        // Inner core
        this.ctx.beginPath();
        this.ctx.arc(x, y, Math.max(size * 0.3, 0.5), 0, Math.PI * 2);
        this.ctx.fill();

        // Outer glow
        this.ctx.globalAlpha *= 0.6;
        this.ctx.shadowBlur *= 2;
        this.ctx.beginPath();
        this.ctx.arc(x, y, Math.max(size, 0.5), 0, Math.PI * 2);
        this.ctx.fill();
      } else {
        this.ctx.beginPath();
        this.ctx.arc(x, y, Math.max(size, 0.5), 0, Math.PI * 2);
        this.ctx.fill();
      }

      this.ctx.restore();
    });
  }
  
  drawComets() {
    this.comets.forEach(comet => {
      // Draw trail
      comet.trail.forEach((point, index) => {
        const alpha = (point.life * index) / comet.trail.length;
        this.ctx.save();
        this.ctx.globalAlpha = alpha;
        this.ctx.fillStyle = comet.color;
        this.ctx.shadowColor = comet.color;
        this.ctx.shadowBlur = 5;
        
        this.ctx.beginPath();
        this.ctx.arc(point.x, point.y, comet.size * alpha, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.restore();
      });
      
      // Draw comet head
      this.ctx.save();
      this.ctx.globalAlpha = comet.life;
      this.ctx.fillStyle = comet.color;
      this.ctx.shadowColor = comet.color;
      this.ctx.shadowBlur = 10;
      
      this.ctx.beginPath();
      this.ctx.arc(comet.x, comet.y, comet.size, 0, Math.PI * 2);
      this.ctx.fill();
      this.ctx.restore();
    });
  }
  
  drawMeteors() {
    this.meteors.forEach(meteor => {
      // Draw trail
      meteor.trail.forEach((point, index) => {
        const alpha = (point.life * index) / meteor.trail.length;
        this.ctx.save();
        this.ctx.globalAlpha = alpha;
        this.ctx.fillStyle = meteor.color;
        this.ctx.shadowColor = meteor.color;
        this.ctx.shadowBlur = 8;
        
        this.ctx.beginPath();
        this.ctx.arc(point.x, point.y, meteor.size * alpha, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.restore();
      });
      
      // Draw sparkles
      meteor.sparkles.forEach(sparkle => {
        this.ctx.save();
        this.ctx.globalAlpha = sparkle.life;
        this.ctx.fillStyle = '#FFFFFF';
        this.ctx.shadowColor = '#FFFFFF';
        this.ctx.shadowBlur = 3;
        
        this.ctx.beginPath();
        this.ctx.arc(sparkle.x, sparkle.y, sparkle.size, 0, Math.PI * 2);
        this.ctx.fill();
        this.ctx.restore();
      });
      
      // Draw meteor head
      this.ctx.save();
      this.ctx.globalAlpha = meteor.life;
      this.ctx.fillStyle = meteor.color;
      this.ctx.shadowColor = meteor.color;
      this.ctx.shadowBlur = 15;
      
      this.ctx.beginPath();
      this.ctx.arc(meteor.x, meteor.y, meteor.size, 0, Math.PI * 2);
      this.ctx.fill();
      this.ctx.restore();
    });
  }
  
  render() {
    // 🛡️ PROTECTED BACKGROUND RENDERING - PREVENT WARP FROM BREAKING BACKGROUND
    try {
      // Clear canvas with background preservation
      this.ctx.save();
      this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

      // 🌟 DRAW BACKGROUND ELEMENTS FIRST (ALWAYS VISIBLE)
      this.drawStars();

      // 🚀 DRAW WARP EFFECTS ONLY WHEN ACTIVE (OVERLAY)
      if (this.config.warpSpeed || this.config.hyperSpace) {
        this.drawWarpRings();
      }

      // 💫 DRAW FOREGROUND EFFECTS
      this.drawComets();
      this.drawMeteors();

      this.ctx.restore();
    } catch (error) {
      console.error('[StarfieldAnimation] ❌ Render error:', error);
      // Fallback: just draw stars to maintain background
      this.drawStars();
    }
  }

  drawWarpRings() {
    if (!this.warpRings) return;

    this.warpRings.forEach((ring, index) => {
      this.ctx.save();
      this.ctx.strokeStyle = ring.color;
      this.ctx.lineWidth = 3;
      this.ctx.beginPath();
      this.ctx.arc(ring.x, ring.y, ring.radius, 0, Math.PI * 2);
      this.ctx.stroke();
      this.ctx.restore();

      // Expand ring
      ring.radius += 15;
      ring.life -= 0.02;

      // Remove dead rings
      if (ring.life <= 0 || ring.radius > ring.maxRadius) {
        this.warpRings.splice(index, 1);
      }
    });
  }
  
  animate() {
    if (!this.isRunning || !this.ctx || !this.canvas) {
      console.warn('[StarfieldAnimation] Animation stopped - missing context or canvas');
      return;
    }

    try {
      // Clear canvas with space background
      this.ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
      this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

      this.updateStars();
      this.updateComets();
      this.updateMeteors();
      this.render();

      this.animationId = requestAnimationFrame(() => this.animate());
    } catch (error) {
      console.error('[StarfieldAnimation] Animation error:', error);
      this.isRunning = false;
    }
  }

  start() {
    if (this.isRunning) {
      console.log('[StarfieldAnimation] Animation already running');
      return;
    }

    if (!this.canvas || !this.ctx) {
      console.error('[StarfieldAnimation] ❌ Cannot start - missing canvas or context');
      return;
    }

    this.isRunning = true;
    this.animate();
    console.log('[StarfieldAnimation] 🌟 Starfield animation started successfully');
  }
  
  stop() {
    this.isRunning = false;
    if (this.animationId) {
      cancelAnimationFrame(this.animationId);
      this.animationId = null;
    }
    console.log('🌟 Starfield animation stopped');
  }
  
  // Public method to update ML signal state
  updateMLSignal(signal, intensity = 0.5) {
    this.mlSignalState = signal; // 'buy', 'sell', 'neutral'
    this.signalIntensity = Math.max(0, Math.min(1, intensity));

    console.log(`🌟 Starfield ML Signal: ${signal} (intensity: ${intensity.toFixed(2)})`);

    // Trigger special effects based on signal strength
    if (intensity > 0.9) {
      // DEGEN MODE: Maximum chaos
      this.triggerQuantumBurst();
      this.triggerHyperSpace(2000);
      this.triggerCometStorm(signal, 8);
    } else if (intensity > 0.8) {
      // High intensity: Warp speed + comet burst
      this.triggerWarpSpeed(1500);
      this.triggerCometStorm(signal, 5);
    } else if (intensity > 0.7) {
      // Medium-high intensity: Comet burst
      this.triggerCometStorm(signal, 3);
    } else if (intensity > 0.5) {
      // Medium intensity: Single comet
      this.comets.push(this.createComet(signal));
    }

    // Add meteors for sell signals (dramatic effect)
    if (signal === 'sell' && intensity > 0.6) {
      for (let i = 0; i < Math.floor(intensity * 3); i++) {
        setTimeout(() => {
          this.meteors.push(this.createMeteor());
        }, i * 300);
      }
    }
  }

  triggerQuantumBurst() {
    this.config.quantumBurst = true;
    console.log('🌟 QUANTUM BURST ACTIVATED!');
    setTimeout(() => {
      this.config.quantumBurst = false;
    }, 3000);
  }

  triggerWarpSpeed(duration = 1000) {
    this.config.warpSpeed = true;
    console.log('🌟 WARP SPEED ENGAGED!');
    setTimeout(() => {
      this.config.warpSpeed = false;
    }, duration);
  }

  triggerHyperSpace(duration = 1500) {
    this.config.hyperSpace = true;
    console.log('🌟 HYPERSPACE JUMP!');
    setTimeout(() => {
      this.config.hyperSpace = false;
    }, duration);
  }

  triggerCometStorm(signal, count = 5) {
    console.log(`🌟 COMET STORM: ${count} ${signal} comets incoming!`);
    for (let i = 0; i < count; i++) {
      setTimeout(() => {
        this.comets.push(this.createComet(signal));
      }, i * 150);
    }
  }
  
  bindEvents() {
    // Resize handler
    window.addEventListener('resize', () => {
      this.resizeCanvas();
      this.generateStars(); // Regenerate stars for new canvas size
    });
    
    // Visibility change handler
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.stop();
      } else {
        this.start();
      }
    });
  }
  
  destroy() {
    this.stop();
    if (this.canvas && this.canvas.parentNode) {
      this.canvas.parentNode.removeChild(this.canvas);
    }
  }
}

// Initialize starfield animation
window.StarfieldAnimation = StarfieldAnimation;

// Auto-initialize when DOM is ready
document.addEventListener('DOMContentLoaded', () => {
  if (!window.starfieldAnimation) {
    window.starfieldAnimation = new StarfieldAnimation();
    console.log('🌟 Starfield animation initialized');
  }
});

// Export for module systems
if (typeof module !== 'undefined' && module.exports) {
  module.exports = StarfieldAnimation;
}

// Initialize starfield animation when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    console.log('[StarfieldAnimation] 🌟 DOM ready - initializing starfield...');
    window.starfieldAnimation = new StarfieldAnimation();
  });
} else {
  console.log('[StarfieldAnimation] 🌟 DOM already ready - initializing starfield...');
  window.starfieldAnimation = new StarfieldAnimation();
}
