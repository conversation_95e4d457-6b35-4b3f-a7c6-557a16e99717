(function() {
  'use strict';

  console.log('🎚️ Enhanced Threshold Sliders initializing...');

  // Default values for various indicators
  const SLIDER_DEFAULTS = {
    rsi: { min: 0, max: 100, low: 30, high: 70 },
    stochRsi: { min: 0, max: 100, low: 20, high: 80 },
    macd: { min: -1, max: 1, low: -0.5, high: 0.5 }, // Note: MACD values are not standardized, may need adjustment
    bollingerBands: { min: 0, max: 1, low: 0.05, high: 0.95 },
    atr: { min: 0, max: 1, low: 0.2, high: 0.8 },
    volume: { min: 0, max: 100, low: 30, high: 70 },
    mfi: { min: 0, max: 100, low: 20, high: 80 },
    williamsR: { min: -100, max: 0, low: -80, high: -20 },
    cci: { min: -200, max: 200, low: -100, high: 100 },
    ultimateOscillator: { min: 0, max: 100, low: 30, high: 70 },
    adx: { min: 0, max: 100, low: 20, high: 50 },
    mlPrediction: { min: 0, max: 1, low: 0.45, high: 0.55 },
  };

  function getSliderDefaults(indicator) {
    return SLIDER_DEFAULTS[indicator] || { min: 0, max: 100, low: 30, high: 70 };
  }

  // Function to save thresholds to the global strategy configuration
  function saveThresholds(strategy, indicator, low, high) {
    if (!window.strategyConfig[strategy]) {
        window.strategyConfig[strategy] = { indicators: [], thresholds: {} };
    }
    if (!window.strategyConfig[strategy].thresholds) {
      window.strategyConfig[strategy].thresholds = {};
    }
    window.strategyConfig[strategy].thresholds[indicator] = { low, high };
  }

  // Update the visual segments of a single slider
  function updateSliderSegments(indicator, values) {
      const container = document.querySelector(`.threshold-slider-container[data-indicator="${indicator}"]`);
      if (!container) {
          // This can happen if the UI isn't fully rendered yet, so not a critical error.
          return;
      }

      const lowSegment = container.querySelector('.slider-segment-low');
      const midSegment = container.querySelector('.slider-segment-mid');
      const highSegment = container.querySelector('.slider-segment-high');
      const defaults = getSliderDefaults(indicator);

      const lowPercent = ((values.low - defaults.min) / (defaults.max - defaults.min)) * 100;
      const highPercent = ((values.high - defaults.min) / (defaults.max - defaults.min)) * 100;

      lowSegment.style.width = `${lowPercent}%`;
      midSegment.style.left = `${lowPercent}%`;
      midSegment.style.width = `${highPercent - lowPercent}%`;
      highSegment.style.left = `${highPercent}%`;
      highSegment.style.width = `${100 - highPercent}%`;
  }
  
  // Make slider thumbs draggable
  function makeThumbsDraggable(sliderContainer, indicator, strategy) {
    const lowThumb = sliderContainer.querySelector('.low-thumb');
    const highThumb = sliderContainer.querySelector('.high-thumb');
    const track = sliderContainer.querySelector('.slider-track');
    const lowValueDisplay = sliderContainer.querySelector('.low-value');
    const highValueDisplay = sliderContainer.querySelector('.high-value');
    const defaults = getSliderDefaults(indicator);

    function setupDraggable(thumb, isLowThumb) {
      thumb.addEventListener('mousedown', (e) => {
        e.preventDefault();
        document.body.style.cursor = 'grabbing';

        function onMouseMove(moveEvent) {
          const trackRect = track.getBoundingClientRect();
          let newX = moveEvent.clientX - trackRect.left;
          let percent = (newX / trackRect.width) * 100;
          percent = Math.max(0, Math.min(100, percent));

          const value = parseFloat((defaults.min + (percent / 100) * (defaults.max - defaults.min)).toFixed(2));

          let lowValue = parseFloat(lowValueDisplay.textContent);
          let highValue = parseFloat(highValueDisplay.textContent);

          if (isLowThumb) {
            if (value >= highValue) return;
            lowValueDisplay.textContent = value;
            lowValue = value;
          } else {
            if (value <= lowValue) return;
            highValueDisplay.textContent = value;
            highValue = value;
          }

          thumb.style.left = `${percent}%`;
          updateSliderSegments(indicator, { low: lowValue, high: highValue });
        }

        function onMouseUp() {
          document.removeEventListener('mousemove', onMouseMove);
          document.removeEventListener('mouseup', onMouseUp);
          document.body.style.cursor = 'default';
          // Save final values after dragging stops
          saveThresholds(strategy, indicator, parseFloat(lowValueDisplay.textContent), parseFloat(highValueDisplay.textContent));
        }

        document.addEventListener('mousemove', onMouseMove);
        document.addEventListener('mouseup', onMouseUp);
      });
    }

    setupDraggable(lowThumb, true);
    setupDraggable(highThumb, false);
  }

  // Create the HTML for a single slider
  function createSliderHTML(indicator, strategy) {
    const defaults = getSliderDefaults(indicator);
    const savedThresholds = window.strategyConfig[strategy]?.thresholds?.[indicator] || defaults;
    const lowPercent = ((savedThresholds.low - defaults.min) / (defaults.max - defaults.min)) * 100;
    const highPercent = ((savedThresholds.high - defaults.min) / (defaults.max - defaults.min)) * 100;

    return `
      <div class="threshold-slider-container" data-indicator="${indicator}">
        <label>${indicator.toUpperCase()}</label>
        <div class="slider-track">
          <div class="slider-segment-low"></div>
          <div class="slider-segment-mid"></div>
          <div class="slider-segment-high"></div>
          <div class="slider-thumb low-thumb" style="left: ${lowPercent}%;"></div>
          <div class="slider-thumb high-thumb" style="left: ${highPercent}%;"></div>
        </div>
        <div class="slider-values">
          <span class="low-value">${savedThresholds.low}</span>
          <span class="high-value">${savedThresholds.high}</span>
        </div>
      </div>
    `;
  }

  // Render all sliders for a given strategy
  function renderThresholdSliders(strategy) {
    if (!strategy || !window.strategyConfig || !window.strategyConfig[strategy]) {
        console.warn(`[EnhancedThresholdSliders] Invalid or missing strategy: ${strategy}. Cannot render sliders.`);
        return;
    }
    
    const container = document.getElementById('threshold-sliders');
    if (!container) {
      console.warn('[EnhancedThresholdSliders] Container not found: threshold-sliders');
      return;
    }

    console.log(`🎚️ ENHANCED THRESHOLD SLIDERS: Rendering for strategy: ${strategy}`);
    const indicators = window.strategyConfig[strategy]?.indicators || [];
    if (!indicators.length) {
        container.innerHTML = '<p class="no-indicators-message">No configurable indicators for this strategy.</p>';
        return;
    }

    let html = '';
    indicators.forEach(indicator => {
      html += createSliderHTML(indicator, strategy);
    });

    container.innerHTML = html;

    // Attach event listeners and update segments after rendering
    indicators.forEach(indicator => {
      const sliderContainer = container.querySelector(`.threshold-slider-container[data-indicator="${indicator}"]`);
      if (sliderContainer) {
        makeThumbsDraggable(sliderContainer, indicator, strategy);
        const lowValue = parseFloat(sliderContainer.querySelector('.low-value').textContent);
        const highValue = parseFloat(sliderContainer.querySelector('.high-value').textContent);
        updateSliderSegments(indicator, { low: lowValue, high: highValue });
      }
    });
  }

  // Export to global scope
  window.EnhancedThresholdSliders = {
    render: renderThresholdSliders,
  };

  console.log('🎚️ Enhanced Threshold Sliders initialized and ready.');

})();
 * Provides threshold slider controls for indicator values
 * Based on 05.06 Gemini backup thresholds-menu.js
 */

(function () {
  // Flags to prevent recursive updates
  window.isUpdatingSlidersProgrammatically = false;
  window.isUpdatingSignalLightsFromThresholds = false;

  // Ensure global objects and helper functions are available
  window.TRADING_STRATEGIES = window.TRADING_STRATEGIES || {};
  window.INDICATORS = window.INDICATORS || { momentum: [], trend: [], volume: [], ml: [] };
  window.logMessages = window.logMessages || [];

  window.updateLogger = window.updateLogger || function () { console.log('[Stub] updateLogger called'); };
  window.showToast = window.showToast || function (message, type) { console.log(`[Stub] Toast: ${message} (${type})`); };
  window.updateAllSignalLights = window.updateAllSignalLights || function () { console.log('[Stub] updateAllSignalLights called'); };

  // Default thresholds for indicators
  window.defaultThresholds = {
    rsi: { red: 70, orange: 60, blue: 40, green: 30 },
    stochRsi: { red: 80, orange: 65, blue: 35, green: 20 },
    williamsR: { red: 80, orange: 65, blue: 35, green: 20 },
    ultimateOscillator: { red: 70, orange: 60, blue: 40, green: 30 },
    mfi: { red: 80, orange: 65, blue: 35, green: 20 },
    adx: { red: 80, orange: 65, blue: 35, green: 20 },
    bollingerBands: { red: 98, orange: 96, blue: 4, green: 2 },
    atr: { high: 2, moderate: 1, low: 0.5 },
    macd: { red: 70, orange: 60, blue: 40, green: 30 },
    volume: { red: 80, orange: 65, blue: 35, green: 20 },
    sentiment: { red: 80, orange: 65, blue: 35, green: 20 },
    entropy: { red: 80, orange: 65, blue: 35, green: 20 },
    correlation: { red: 80, orange: 65, blue: 35, green: 20 },
    time_anomaly: { red: 80, orange: 65, blue: 35, green: 20 },
  };

  // Load saved thresholds or use defaults
  if (typeof window.thresholds === 'undefined') {
    const savedThresholds = localStorage.getItem('userThresholds');
    if (savedThresholds) {
      try {
        window.thresholds = JSON.parse(savedThresholds);
        for (const key in window.defaultThresholds) {
          if (!window.thresholds[key]) {
            window.thresholds[key] = JSON.parse(JSON.stringify(window.defaultThresholds[key]));
          }
          if (window.defaultThresholds[key].red !== undefined && window.thresholds[key].red === undefined && key !== 'atr') {
            window.thresholds[key] = JSON.parse(JSON.stringify(window.defaultThresholds[key]));
          }
        }
      } catch (e) {
        window.logMessages.push(`[${new Date().toLocaleString()}] Error loading/parsing saved thresholds: ${e.message}`);
        window.updateLogger();
        window.thresholds = JSON.parse(JSON.stringify(window.defaultThresholds));
      }
    } else {
      window.thresholds = JSON.parse(JSON.stringify(window.defaultThresholds));
    }
  }

  // Update the visual representation of threshold segments for a single slider
  function updateThresholdDisplay(sliderContainer) {
    try {
      const indicatorName = sliderContainer.dataset.indicator;
      const thresholds = window.thresholds[indicatorName];

      if (!thresholds) return;

      // Get the segment elements
      const greenSegment = sliderContainer.querySelector('.green-segment');
      const blueSegment = sliderContainer.querySelector('.blue-segment');
      const graySegment = sliderContainer.querySelector('.gray-segment');
      const orangeSegment = sliderContainer.querySelector('.orange-segment');
      const redSegment = sliderContainer.querySelector('.red-segment');

      // Calculate the widths of each segment
      greenSegment.style.width = `${thresholds.green}%`;
      blueSegment.style.width = `${thresholds.blue - thresholds.green}%`;
      graySegment.style.width = `${thresholds.orange - thresholds.blue}%`;
      orangeSegment.style.width = `${thresholds.red - thresholds.orange}%`;
      redSegment.style.width = `${100 - thresholds.red}%`;

      // Set left positions
      blueSegment.style.left = `${thresholds.green}%`;
      graySegment.style.left = `${thresholds.blue}%`;
      orangeSegment.style.left = `${thresholds.orange}%`;
      redSegment.style.left = `${thresholds.red}%`;
    } catch (error) {
      console.error('Error updating threshold display:', error);
    }
  }

  // Update all threshold displays
  function updateAllThresholdDisplays() {
    const sliderContainers = document.querySelectorAll('.slider-container');
    sliderContainers.forEach(updateThresholdDisplay);
  }

  // Add reset button to revert to default thresholds
  function addResetButton(parentElement) {
    const resetContainer = document.createElement('div');
    resetContainer.className = 'reset-thresholds-container';
    resetContainer.style.textAlign = 'center';
    resetContainer.style.marginTop = '15px';

    const resetButton = document.createElement('button');
    resetButton.textContent = 'Reset to Defaults';
    resetButton.className = 'menu-button';
    resetButton.style.padding = '5px 10px';
    resetButton.style.margin = '0 auto';

    resetButton.addEventListener('click', function() {
      // Reset thresholds to defaults
      window.thresholds = JSON.parse(JSON.stringify(window.defaultThresholds));
      localStorage.setItem('userThresholds', JSON.stringify(window.thresholds));

      // Re-render sliders
      renderThresholdSliders(window.currentStrategy);

      // Update signal lights
      if (typeof window.updateAllSignalLights === 'function') {
        window.updateAllSignalLights();
      }

      window.showToast('Thresholds reset to defaults', 'info');
    });

    resetContainer.appendChild(resetButton);
    parentElement.appendChild(resetContainer);
  }

  /**
   * Ensures the threshold menu container exists
   */
  function ensureThresholdsMenuContainerExists() {
    // Use thresholdsMenu ID to match what menu-controller.js expects
    let container = document.getElementById('thresholdsMenu');

    if (!container) {
      console.log('Creating thresholds menu container with ID: thresholdsMenu');
      container = document.createElement('div');
      container.id = 'thresholdsMenu';
      container.className = 'menu-content';
      // Start hidden
      container.style.display = 'none';
      container.style.position = 'absolute';
      container.style.top = '100%';
      container.style.right = '0';
      container.style.width = '320px';
      container.style.backgroundColor = '#1a1a2e';
      container.style.border = '1px solid #303045';
      container.style.zIndex = '1000';
      container.style.padding = '15px';
      container.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.5)';

      // Find the tickerContainer to append to
      const tickerContainer = document.getElementById('tickerContainer');
      if (tickerContainer) {
        tickerContainer.appendChild(container);
        console.log('Created and appended thresholdsMenu container to tickerContainer');
      } else {
        // Fallback to body
        document.body.appendChild(container);
        console.log('Created and appended thresholdsMenu container to body (fallback)');
      }
    }
    return container;
  }

  function renderThresholdSliders(strategy) {
    try {
      // Always ensure container exists with correct ID
      const slidersContainerElement = ensureThresholdsMenuContainerExists();
      slidersContainerElement.innerHTML = ''; // Clear previous content

      const header = document.createElement('h3');
      header.style.cssText = 'font-size: 14px; margin: 5px 0px; padding: 0px;';
      header.textContent = 'Thresholds: Admiral T.O.A. Convergence';
      slidersContainerElement.appendChild(header);

      const currentStrategyName = strategy || window.currentStrategy || 'admiral_toa'; // Ensure currentStrategy is defined
      const strategyIndicators = window.TRADING_STRATEGIES[currentStrategyName] ?
        window.TRADING_STRATEGIES[currentStrategyName].indicators :
        Object.keys(window.thresholds).filter(ind => ind !== 'atr');

      // Create sliders for each indicator in the strategy
      strategyIndicators.forEach(indicatorName => {
        if (window.thresholds[indicatorName] && indicatorName !== 'atr') {
          const container = document.createElement('div');
          container.className = 'slider-container';
          container.dataset.indicator = indicatorName;

          const label = document.createElement('div');
          label.className = 'slider-label';

          // Create human-friendly name with proper capitalization
          let displayName = indicatorName;
          switch (indicatorName) {
            case 'rsi':
              displayName = 'RSI';
              break;
            case 'stochRsi':
              displayName = 'Stoch RSI';
              break;
            case 'williamsR':
              displayName = 'Williams %R';
              break;
            case 'ultimateOscillator':
              displayName = 'Ultimate Oscillator';
              break;
            case 'mfi':
              displayName = 'MFI';
              break;
            case 'adx':
              displayName = 'ADX';
              break;
            case 'bollingerBands':
              displayName = 'Bollinger Bands';
              break;
            case 'macd':
              displayName = 'MACD';
              break;
            case 'volume':
              displayName = 'Volume';
              break;
            case 'sentiment':
              displayName = 'ML Sentiment';
              break;
            case 'entropy':
              displayName = 'ML Entropy';
              break;
            case 'correlation':
              displayName = 'ML Correlation';
              break;
            case 'time_anomaly':
              displayName = 'Time Anomaly';
              break;
            default:
              displayName = indicatorName.charAt(0).toUpperCase() + indicatorName.slice(1);
          }

          label.textContent = displayName;
          container.appendChild(label);

          const sliderBar = document.createElement('div');
          sliderBar.className = 'threshold-slider-bar';

          // Create all the threshold segments
          const segments = {
            green: document.createElement('div'),
            blue: document.createElement('div'),
            gray: document.createElement('div'),
            orange: document.createElement('div'),
            red: document.createElement('div')
          };

          segments.green.className = 'threshold-segment green-segment';
          segments.blue.className = 'threshold-segment blue-segment';
          segments.gray.className = 'threshold-segment gray-segment';
          segments.orange.className = 'threshold-segment orange-segment';
          segments.red.className = 'threshold-segment red-segment';

          // Append all segments to the slider bar
          sliderBar.appendChild(segments.green);
          sliderBar.appendChild(segments.blue);
          sliderBar.appendChild(segments.gray);
          sliderBar.appendChild(segments.orange);
          sliderBar.appendChild(segments.red);

          container.appendChild(sliderBar);

          // Create thumbs for draggable threshold boundaries
          const thresholds = window.thresholds[indicatorName];
          const thumbs = {
            green: document.createElement('div'),
            blue: document.createElement('div'),
            orange: document.createElement('div'),
            red: document.createElement('div')
          };

          thumbs.green.className = 'threshold-thumb green-thumb';
          thumbs.green.dataset.type = 'green';
          thumbs.green.style.left = `${thresholds.green}%`;
          thumbs.green.title = `Green: ${thresholds.green}%`;

          thumbs.blue.className = 'threshold-thumb blue-thumb';
          thumbs.blue.dataset.type = 'blue';
          thumbs.blue.style.left = `${thresholds.blue}%`;
          thumbs.blue.title = `Blue: ${thresholds.blue}%`;

          thumbs.orange.className = 'threshold-thumb orange-thumb';
          thumbs.orange.dataset.type = 'orange';
          thumbs.orange.style.left = `${thresholds.orange}%`;
          thumbs.orange.title = `Orange: ${thresholds.orange}%`;

          thumbs.red.className = 'threshold-thumb red-thumb';
          thumbs.red.dataset.type = 'red';
          thumbs.red.style.left = `${thresholds.red}%`;
          thumbs.red.title = `Red: ${thresholds.red}%`;

          // Add thumbs to container
          container.appendChild(thumbs.green);
          container.appendChild(thumbs.blue);
          container.appendChild(thumbs.orange);
          container.appendChild(thumbs.red);

          // Add threshold labels
          const labels = {
            green: document.createElement('div'),
            blue: document.createElement('div'),
            orange: document.createElement('div'),
            red: document.createElement('div')
          };

          labels.green.className = 'threshold-value green-value';
          labels.green.textContent = `${thresholds.green}%`;
          labels.green.style.left = `${thresholds.green}%`;

          labels.blue.className = 'threshold-value blue-value';
          labels.blue.textContent = `${thresholds.blue}%`;
          labels.blue.style.left = `${thresholds.blue}%`;

          labels.orange.className = 'threshold-value orange-value';
          labels.orange.textContent = `${thresholds.orange}%`;
          labels.orange.style.left = `${thresholds.orange}%`;

          labels.red.className = 'threshold-value red-value';
          labels.red.textContent = `${thresholds.red}%`;
          labels.red.style.left = `${thresholds.red}%`;

          // Add labels to container
          container.appendChild(labels.green);
          container.appendChild(labels.blue);
          container.appendChild(labels.orange);
          container.appendChild(labels.red);

          // Make thumbs draggable
          Object.keys(thumbs).forEach(type => {
            makeThumbDraggable(thumbs[type], indicatorName, sliderBar);
          });

          slidersContainerElement.appendChild(container);
        }
      });

      // Set initial segment widths
      updateAllThresholdDisplays();

      // Add reset button
      addResetButton(slidersContainerElement);
    } catch (error) {
      console.error('Error rendering threshold sliders:', error);
    }
  }

  // Export to global scope
  window.renderThresholdSliders = renderThresholdSliders;
  window.updateAllThresholdDisplays = updateAllThresholdDisplays;

  // Initialize and export functions to global scope
  window.thresholdSliders = {
    render: renderThresholdSliders,
    update: updateAllThresholdDisplays,
    reset: function() {
      window.thresholds = JSON.parse(JSON.stringify(window.defaultThresholds));
      renderThresholdSliders(window.currentStrategy);
    }
  };

  console.log('[ThresholdSliders] Threshold sliders system initialized');
})();
  function updateThresholdDisplay(sliderContainer) {
    try {
      const indicatorName = sliderContainer.dataset.indicator;
      const thresholds = window.thresholds[indicatorName];
      
      if (!thresholds) return;

      // Get the segment elements
      const greenSegment = sliderContainer.querySelector('.green-segment');
      const blueSegment = sliderContainer.querySelector('.blue-segment');
      const graySegment = sliderContainer.querySelector('.gray-segment');
      const orangeSegment = sliderContainer.querySelector('.orange-segment');
      const redSegment = sliderContainer.querySelector('.red-segment');

      // Calculate the widths of each segment
      greenSegment.style.width = `${thresholds.green}%`;
      blueSegment.style.width = `${thresholds.blue - thresholds.green}%`;
      graySegment.style.width = `${thresholds.orange - thresholds.blue}%`;
      orangeSegment.style.width = `${thresholds.red - thresholds.orange}%`;
      redSegment.style.width = `${100 - thresholds.red}%`;
      
      // Set left positions
      blueSegment.style.left = `${thresholds.green}%`;
      graySegment.style.left = `${thresholds.blue}%`;
      orangeSegment.style.left = `${thresholds.orange}%`;
      redSegment.style.left = `${thresholds.red}%`;
    } catch (error) {
      console.error('Error updating threshold display:', error);
    }
  }

  // Update all threshold displays
  function updateAllThresholdDisplays() {
    const sliderContainers = document.querySelectorAll('.slider-container');
    sliderContainers.forEach(updateThresholdDisplay);
  }

  // Add reset button to revert to default thresholds
  function addResetButton(parentElement) {
    const resetContainer = document.createElement('div');
    resetContainer.className = 'reset-thresholds-container';
    resetContainer.style.textAlign = 'center';
    resetContainer.style.marginTop = '15px';

    const resetButton = document.createElement('button');
    resetButton.textContent = 'Reset to Defaults';
    resetButton.className = 'menu-button';
    resetButton.style.padding = '5px 10px';
    resetButton.style.margin = '0 auto';
    
    resetButton.addEventListener('click', function() {
      // Reset thresholds to defaults
      window.thresholds = JSON.parse(JSON.stringify(window.defaultThresholds));
      localStorage.setItem('userThresholds', JSON.stringify(window.thresholds));
      
      // Re-render sliders
      renderThresholdSliders(window.currentStrategy);
      
      // Update signal lights
      if (typeof window.updateAllSignalLights === 'function') {
        window.updateAllSignalLights();
      }
      
      window.showToast('Thresholds reset to defaults', 'info');
    });
    
    resetContainer.appendChild(resetButton);
    parentElement.appendChild(resetContainer);
  }
  
  /**
   * Ensures the threshold menu container exists
   */
  function ensureThresholdsMenuContainerExists() {
    // Use thresholdsMenu ID to match what menu-controller.js expects
    let container = document.getElementById('thresholdsMenu');
    
    if (!container) {
      console.log('Creating thresholds menu container with ID: thresholdsMenu');
      container = document.createElement('div');
      container.id = 'thresholdsMenu';
      container.className = 'menu-content';
      // Start hidden
      container.style.display = 'none';
      container.style.position = 'absolute';
      container.style.top = '100%';
      container.style.right = '0';
      container.style.width = '320px';
      container.style.backgroundColor = '#1a1a2e';
      container.style.border = '1px solid #303045';
      container.style.zIndex = '1000';
      container.style.padding = '15px';
      container.style.boxShadow = '0 4px 8px rgba(0, 0, 0, 0.5)';
      
      // Find the tickerContainer to append to
      const tickerContainer = document.getElementById('tickerContainer');
      if (tickerContainer) {
        tickerContainer.appendChild(container);
        console.log('Created and appended thresholdsMenu container to tickerContainer');
      } else {
        // Fallback to body
        document.body.appendChild(container);
        console.log('Created and appended thresholdsMenu container to body (fallback)');
      }
    }
    return container;
  }

  function renderThresholdSliders(strategy) {
    try {
      // Always ensure container exists with correct ID
      const slidersContainerElement = ensureThresholdsMenuContainerExists();
      slidersContainerElement.innerHTML = ''; // Clear previous content

      const header = document.createElement('h3');
      header.style.cssText = 'font-size: 14px; margin: 5px 0px; padding: 0px;';
      header.textContent = 'Thresholds: Admiral T.O.A. Convergence';
      slidersContainerElement.appendChild(header);

      const currentStrategyName = strategy || window.currentStrategy || 'admiral_toa'; // Ensure currentStrategy is defined
      const strategyIndicators = window.TRADING_STRATEGIES[currentStrategyName] ?
        window.TRADING_STRATEGIES[currentStrategyName].indicators :
        Object.keys(window.thresholds).filter(ind => ind !== 'atr');

      // Create sliders for each indicator in the strategy
      strategyIndicators.forEach(indicatorName => {
        if (window.thresholds[indicatorName] && indicatorName !== 'atr') {
          const container = document.createElement('div');
          container.className = 'slider-container';
          container.dataset.indicator = indicatorName;

          const label = document.createElement('div');
          label.className = 'slider-label';
          
          // Create human-friendly name with proper capitalization
          let displayName = indicatorName;
          switch (indicatorName) {
            case 'rsi':
              displayName = 'RSI';
              break;
            case 'stochRsi':
              displayName = 'Stoch RSI';
              break;
            case 'williamsR':
              displayName = 'Williams %R';
              break;
            case 'ultimateOscillator':
              displayName = 'Ultimate Oscillator';
              break;
            case 'mfi':
              displayName = 'MFI';
              break;
            case 'adx':
              displayName = 'ADX';
              break;
            case 'bollingerBands':
              displayName = 'Bollinger Bands';
              break;
            case 'macd':
              displayName = 'MACD';
              break;
            case 'volume':
              displayName = 'Volume';
              break;
            case 'sentiment':
              displayName = 'ML Sentiment';
              break;
            case 'entropy':
              displayName = 'ML Entropy';
              break;
            case 'correlation':
              displayName = 'ML Correlation';
              break;
            case 'time_anomaly':
              displayName = 'Time Anomaly';
              break;
            default:
              displayName = indicatorName.charAt(0).toUpperCase() + indicatorName.slice(1);
          }
          
          label.textContent = displayName;
          container.appendChild(label);

          const sliderBar = document.createElement('div');
          sliderBar.className = 'threshold-slider-bar';

          // Create all the threshold segments
          const segments = {
            green: document.createElement('div'),
            blue: document.createElement('div'),
            gray: document.createElement('div'),
            orange: document.createElement('div'),
            red: document.createElement('div')
          };

          segments.green.className = 'threshold-segment green-segment';
          segments.blue.className = 'threshold-segment blue-segment';
          segments.gray.className = 'threshold-segment gray-segment';
          segments.orange.className = 'threshold-segment orange-segment';
          segments.red.className = 'threshold-segment red-segment';

          // Append all segments to the slider bar
          sliderBar.appendChild(segments.green);
          sliderBar.appendChild(segments.blue);
          sliderBar.appendChild(segments.gray);
          sliderBar.appendChild(segments.orange);
          sliderBar.appendChild(segments.red);

          container.appendChild(sliderBar);

          // Create thumbs for draggable threshold boundaries
          const thresholds = window.thresholds[indicatorName];
          const thumbs = {
            green: document.createElement('div'),
            blue: document.createElement('div'),
            orange: document.createElement('div'),
            red: document.createElement('div')
          };

          thumbs.green.className = 'threshold-thumb green-thumb';
          thumbs.green.dataset.type = 'green';
          thumbs.green.style.left = `${thresholds.green}%`;
          thumbs.green.title = `Green: ${thresholds.green}%`;

          thumbs.blue.className = 'threshold-thumb blue-thumb';
          thumbs.blue.dataset.type = 'blue';
          thumbs.blue.style.left = `${thresholds.blue}%`;
          thumbs.blue.title = `Blue: ${thresholds.blue}%`;

          thumbs.orange.className = 'threshold-thumb orange-thumb';
          thumbs.orange.dataset.type = 'orange';
          thumbs.orange.style.left = `${thresholds.orange}%`;
          thumbs.orange.title = `Orange: ${thresholds.orange}%`;

          thumbs.red.className = 'threshold-thumb red-thumb';
          thumbs.red.dataset.type = 'red';
          thumbs.red.style.left = `${thresholds.red}%`;
          thumbs.red.title = `Red: ${thresholds.red}%`;

          // Add thumbs to container
          container.appendChild(thumbs.green);
          container.appendChild(thumbs.blue);
          container.appendChild(thumbs.orange);
          container.appendChild(thumbs.red);

          // Add threshold labels
          const labels = {
            green: document.createElement('div'),
            blue: document.createElement('div'),
            orange: document.createElement('div'),
            red: document.createElement('div')
          };

          labels.green.className = 'threshold-value green-value';
          labels.green.textContent = `${thresholds.green}%`;
          labels.green.style.left = `${thresholds.green}%`;

          labels.blue.className = 'threshold-value blue-value';
          labels.blue.textContent = `${thresholds.blue}%`;
          labels.blue.style.left = `${thresholds.blue}%`;

          labels.orange.className = 'threshold-value orange-value';
          labels.orange.textContent = `${thresholds.orange}%`;
          labels.orange.style.left = `${thresholds.orange}%`;

          labels.red.className = 'threshold-value red-value';
          labels.red.textContent = `${thresholds.red}%`;
          labels.red.style.left = `${thresholds.red}%`;

          // Add labels to container
          container.appendChild(labels.green);
          container.appendChild(labels.blue);
          container.appendChild(labels.orange);
          container.appendChild(labels.red);

          // Make thumbs draggable
          Object.keys(thumbs).forEach(type => {
            makeThumbDraggable(thumbs[type], indicatorName, sliderBar);
          });

          slidersContainerElement.appendChild(container);
        }
      });

      // Set initial segment widths
      updateAllThresholdDisplays();

      // Add reset button
      addResetButton(slidersContainerElement);
    } catch (error) {
      console.error('Error rendering threshold sliders:', error);
    }
  }
  // Function to show/hide the threshold sliders menu
  function toggleThresholdsMenu() {
    const thresholdsMenuElement = document.getElementById('threshold-sliders');
    if (!thresholdsMenuElement) {
      console.error('Threshold sliders element not found');
      return;
    }
    
    const isVisible = thresholdsMenuElement.style.display !== 'none' && thresholdsMenuElement.style.display !== '';
    
    // Hide all menus first
    hideAllMenus();
    
    if (!isVisible) {
      // Show this menu
      thresholdsMenuElement.style.display = 'block';
      renderThresholdSliders(window.currentStrategy);
      document.getElementById('toggleThresholdsButton')?.classList.add('active');
    }
  }

  // Helper function to hide all menus
  function hideAllMenus() {
    const menus = [
      'threshold-sliders',
      'strategyMenu',
      'indicatorMenu',
      'logicControls'
    ];
    
    menus.forEach(menuId => {
      const element = document.getElementById(menuId);
      if (element) {
        element.style.display = 'none';
      }
    });
    
    // Remove active class from all buttons
  
    document.querySelectorAll('.menu-button').forEach(button => {
      button.classList.remove('active');
    });
  }

// Updates the existing thresholdsMenu or creates it if missing
// This version checks for dynamically created containers from menu-controller.js first
function ensureThresholdsMenuExists() {
  console.log('[ThresholdSliders] Ensuring thresholdsMenu exists');
  // First check if menu-controller.js already created the container
  let thresholdsMenu = document.getElementById('thresholdsMenu');
  
  if (!thresholdsMenu) {
    console.log('[ThresholdSliders] Creating thresholdsMenu container');
    thresholdsMenu = document.createElement('div');
    thresholdsMenu.id = 'thresholdsMenu';
    thresholdsMenu.className = 'menu-container thresholds-menu';
    thresholdsMenu.style.display = 'none';
    
    // Add to document body or appropriate container
    const tickerContainer = document.getElementById('tickerContainer');
    if (tickerContainer) {
      tickerContainer.appendChild(thresholdsMenu);
    } else {
      document.body.appendChild(thresholdsMenu);
    }
  } else {
    console.log('[ThresholdSliders] Found existing thresholdsMenu container');
  }
  
  // Set up bidirectional aliases between thresholdsMenu and threshold-sliders
  // for backwards compatibility
  const thresholdSliders = document.getElementById('threshold-sliders');
  
  if (!thresholdSliders) {
    // Create a data attribute to identify the relationship
    thresholdsMenu.setAttribute('data-alias', 'threshold-sliders');
  } else if (thresholdSliders !== thresholdsMenu) {
    console.log('[ThresholdSliders] Found separate threshold-sliders element, will merge content');
    // Move any existing content from threshold-sliders to thresholdsMenu
    while (thresholdSliders.firstChild) {
      thresholdsMenu.appendChild(thresholdSliders.firstChild);
    }
    // Remove the redundant container
    thresholdSliders.parentNode.removeChild(thresholdSliders);
  }
  
  return thresholdsMenu;
}

// Setup any click handlers specific to this component
function setupThresholdMenuButtons() {
  const toggleButton = document.getElementById('toggleThresholdsButton');
  if (toggleButton) {
      toggleButton.addEventListener('click', toggleThresholdsMenu);
    }
}

// Export to global scope
  // Initialize and export functions to global scope
  window.thresholdSliders = {
    render: renderThresholdSliders,
    toggle: toggleThresholdsMenu,
    update: updateAllThresholdDisplays,
    reset: function() {
      window.thresholds = JSON.parse(JSON.stringify(window.defaultThresholds));
      renderThresholdSliders(window.currentStrategy);
    }
  };

  // Initialize components
  ensureThresholdsMenuExists();
  setupThresholdMenuButtons();
})();
