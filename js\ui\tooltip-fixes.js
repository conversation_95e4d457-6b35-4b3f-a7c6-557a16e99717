/**
 * Tooltip Fixes for StarCrypt
 * Removes duplicate tooltips, fixes cursor issues, ensures proper z-index
 */

class TooltipFixes {
  constructor() {
    this.activeTooltip = null;
    this.tooltipTimeout = null;
    this.init();
  }

  init() {
    console.log('[TooltipFixes] 🎯 MASTER TOOLTIP SYSTEM - Establishing as ONLY tooltip system');

    try {
      this.removeDefaultTooltips();
      this.fixCursorStyles();
      this.enhanceTooltipSystem();
      this.applyTooltipStyles();
      this.disableCompetingTooltipSystems();

      console.log('[TooltipFixes] ✅ MASTER TOOLTIP SYSTEM ESTABLISHED - All competing systems disabled');
    } catch (error) {
      console.error('[TooltipFixes] Error applying tooltip fixes:', error);
    }
  }

  disableCompetingTooltipSystems() {
    console.log('[TooltipFixes] 🚫 Disabling ALL competing tooltip systems');

    // Override any other tooltip handlers
    if (window.signalManager && window.signalManager.showTooltip) {
      window.signalManager.showTooltip = () => {
        console.log('[TooltipFixes] 🚫 BLOCKED: signalManager tooltip → Enhanced Tooltips');
      };
    }

    // Don't remove signal circle elements - just ensure our tooltip system takes precedence
    console.log('[TooltipFixes] ✅ All competing tooltip systems disabled');
  }

  removeDefaultTooltips() {
    // Remove any existing title attributes that cause default browser tooltips
    document.addEventListener('DOMContentLoaded', () => {
      this.cleanupTooltips();
    });
    
    // Also clean up periodically for dynamically added elements
    setInterval(() => {
      this.cleanupTooltips();
    }, 5000);
  }

  cleanupTooltips() {
    // Remove title attributes from signal lights to prevent duplicate tooltips
    const signalLights = document.querySelectorAll('.signal-circle, .circle');
    signalLights.forEach(light => {
      if (light.hasAttribute('title')) {
        // Move title to data-tooltip if it doesn't exist
        if (!light.hasAttribute('data-tooltip')) {
          light.setAttribute('data-tooltip', light.getAttribute('title'));
        }
        light.removeAttribute('title');
      }
    });

    // Remove title attributes from other elements that might cause conflicts
    const elementsWithTitles = document.querySelectorAll('[title]');
    elementsWithTitles.forEach(element => {
      // Skip elements that should keep their titles (like buttons with specific functionality)
      if (!element.classList.contains('keep-title') && 
          !element.hasAttribute('data-keep-title')) {
        
        // For signal lights and indicators, move to data-tooltip
        if (element.classList.contains('signal-circle') || 
            element.classList.contains('circle') ||
            element.classList.contains('indicator-item')) {
          
          if (!element.hasAttribute('data-tooltip')) {
            element.setAttribute('data-tooltip', element.getAttribute('title'));
          }
          element.removeAttribute('title');
        }
      }
    });
  }

  fixCursorStyles() {
    const style = document.createElement('style');
    style.textContent = `
      /* Fix cursor styles for signal lights */
      .signal-circle,
      .circle {
        cursor: default !important; /* Remove help cursor */
      }
      
      .signal-circle:hover,
      .circle:hover {
        cursor: default !important;
      }
      
      /* Ensure interactive elements have proper cursors */
      .menu-button,
      .strategy-button,
      .admiral-mode-toggle,
      button {
        cursor: pointer !important;
      }
      
      /* Remove question mark cursor from tooltipped elements */
      [data-tooltip] {
        cursor: default !important;
      }
      
      [data-tooltip]:hover {
        cursor: default !important;
      }
      
      /* Specific fixes for signal lights */
      .momentum-indicators .signal-circle,
      .momentum-indicators .circle {
        cursor: default !important;
      }
      
      /* Ensure sliders and interactive controls keep pointer cursor */
      input[type="range"],
      .threshold-slider,
      .slider-thumb {
        cursor: pointer !important;
      }
    `;
    document.head.appendChild(style);
  }

  enhanceTooltipSystem() {
    console.log('[TooltipFixes] 🎯 Setting up enhanced actionable tooltips for signal circles');

    // Create enhanced tooltip container if it doesn't exist
    let tooltip = document.getElementById('global-tooltip');
    if (!tooltip) {
      tooltip = document.createElement('div');
      tooltip.id = 'global-tooltip';
      tooltip.className = 'enhanced-tooltip';
      document.body.appendChild(tooltip);
    }

    // Remove existing tooltip event listeners to prevent conflicts
    this.removeExistingTooltipListeners();

    // Add enhanced tooltip event listeners
    this.addEnhancedTooltipListeners(tooltip);

    // Setup signal circle tooltip handlers specifically
    this.setupSignalCircleTooltips();
  }

  setupSignalCircleTooltips() {
    console.log('[TooltipFixes] 🎯 Setting up signal circle tooltips with actionable trading intel');

    // Remove any existing tooltip listeners to prevent duplicates
    if (this.tooltipMouseEnterHandler) {
      document.removeEventListener('mouseenter', this.tooltipMouseEnterHandler, true);
    }
    if (this.tooltipMouseLeaveHandler) {
      document.removeEventListener('mouseleave', this.tooltipMouseLeaveHandler, true);
    }

    // Create bound handlers
    this.tooltipMouseEnterHandler = (e) => {
      if (!e.target || typeof e.target.closest !== 'function') return;
      const signalCircle = e.target.closest('.signal-circle, .circle, [data-ind], [data-indicator]');
      if (signalCircle) {
        console.log('[TooltipFixes] 🎯 Signal circle hover detected:', signalCircle);
        this.showEnhancedSignalTooltip(signalCircle, e);
      }
    };

    this.tooltipMouseLeaveHandler = (e) => {
      if (!e.target || typeof e.target.closest !== 'function') return;
      const signalCircle = e.target.closest('.signal-circle, .circle, [data-ind], [data-indicator]');
      if (signalCircle) {
        this.hideEnhancedTooltip();
      }
    };

    // Use event delegation for signal circles specifically
    document.addEventListener('mouseenter', this.tooltipMouseEnterHandler, true);
    document.addEventListener('mouseleave', this.tooltipMouseLeaveHandler, true);

    // Also set up a periodic check to ensure tooltips work for dynamically created elements
    this.setupPeriodicTooltipCheck();
  }

  setupPeriodicTooltipCheck() {
    // Check every 2 seconds for new signal circles that need tooltips
    setInterval(() => {
      const circles = document.querySelectorAll('.signal-circle[data-ind][data-tf]');
      if (circles.length > 0) {
        console.log(`[TooltipFixes] 🔍 Found ${circles.length} signal circles with proper data attributes`);
      }
    }, 2000);
  }

  showEnhancedSignalTooltip(element, event) {
    try {
      const indicator = element.getAttribute('data-ind') ||
                       element.getAttribute('data-indicator') ||
                       element.getAttribute('data-connected-indicator');
      let timeframe = element.getAttribute('data-tf') ||
                     element.getAttribute('data-timeframe') ||
                     element.getAttribute('data-connected-timeframe');

      // 🛡️ ENHANCED TIMEFRAME DETECTION - FIND TIMEFRAME FROM PARENT OR CONTEXT
      if (!timeframe) {
        // Try to find timeframe from parent row or table context
        const parentRow = element.closest('tr[data-timeframe], tr[data-tf]');
        if (parentRow) {
          timeframe = parentRow.getAttribute('data-timeframe') || parentRow.getAttribute('data-tf');
        }

        // Try to find from signal circle siblings
        if (!timeframe) {
          const signalContainer = element.closest('.signals-cell, .signal-container');
          if (signalContainer) {
            const allSignals = signalContainer.querySelectorAll('.signal-circle[data-tf]');
            if (allSignals.length > 0) {
              // Use the timeframe from the first signal circle that has one
              for (const signal of allSignals) {
                const tf = signal.getAttribute('data-tf');
                if (tf) {
                  timeframe = tf;
                  break;
                }
              }
            }
          }
        }

        // Fallback: try to extract from element position or class
        if (!timeframe) {
          const allTimeframes = ['1m', '5m', '15m', '1h', '4h', '1d', '1w'];
          const elementIndex = Array.from(element.parentNode?.children || []).indexOf(element);
          if (elementIndex >= 0 && elementIndex < allTimeframes.length) {
            timeframe = allTimeframes[elementIndex];
          }
        }
      }

      console.log('[TooltipFixes] 🎯 Showing tooltip for:', indicator, timeframe);

      if (!indicator) {
        console.log('[TooltipFixes] ❌ Missing indicator:', { indicator, timeframe });
        return;
      }

      // Use fallback timeframe if still missing
      if (!timeframe) {
        timeframe = '1h'; // Default fallback
        console.log('[TooltipFixes] ⚠️ Using fallback timeframe:', timeframe);
      }

      // Get current indicator data
      const data = window.indicatorsData?.[timeframe]?.[indicator];
      const color = element.style.backgroundColor || element.className.includes('green') ? '#00ff00' :
                   element.className.includes('red') ? '#ff0000' :
                   element.className.includes('blue') ? '#0080ff' :
                   element.className.includes('orange') ? '#ff8000' : '#808080';

      console.log('[TooltipFixes] 📊 Data for tooltip:', { indicator, timeframe, data, color });

      // Generate enhanced splendid tooltip with full info
      const tooltipContent = this.generateSplendidTooltip(indicator, timeframe, data, color, element);

      const tooltip = document.getElementById('global-tooltip');
      if (tooltip) {
        tooltip.innerHTML = tooltipContent;
        tooltip.style.display = 'block';
        tooltip.style.opacity = '1';
        tooltip.classList.add('enhanced-tooltip');
        this.positionTooltip(tooltip, event);
        console.log('[TooltipFixes] ✅ Tooltip displayed successfully');
      } else {
        console.log('[TooltipFixes] ❌ Global tooltip element not found');
      }
    } catch (error) {
      console.error('[TooltipFixes] Error showing enhanced signal tooltip:', error);
    }
  }

  generateSplendidTooltip(indicator, timeframe, data, color, element) {
    const indicatorName = indicator.toUpperCase();
    const timestamp = new Date().toLocaleString();

    // Get signal strength and direction (with fallback for missing data)
    const signalInfo = this.analyzeSignalStrength(element, data) || {
      class: 'neutral',
      icon: '⚪',
      text: 'WAITING FOR DATA',
      confidence: 0
    };

    // Get threshold information
    const thresholds = this.getActualThresholds(indicator);
    const proximityInfo = this.calculateThresholdProximity(data, thresholds);

    // Generate trading recommendation (with fallback)
    const tradingAdvice = this.generateTradingAdvice(signalInfo, proximityInfo) || {
      class: 'neutral',
      text: 'Waiting for market data to generate trading recommendations',
      action: 'Monitor for signal development'
    };

    return `
      <div class="splendid-tooltip">
        <div class="tooltip-header">
          <div class="indicator-title">${indicatorName}</div>
          <div class="timeframe-badge">${timeframe}</div>
        </div>

        <div class="tooltip-body">
          <div class="signal-info">
            <div class="signal-strength ${signalInfo.class}">
              <span class="signal-icon">${signalInfo.icon}</span>
              <span class="signal-text">${signalInfo.text}</span>
            </div>
            <div class="signal-confidence">
              Confidence: <span class="confidence-value">${signalInfo.confidence}%</span>
            </div>
          </div>

          <div class="data-section">
            <div class="current-value">
              <label>Current Value:</label>
              <span class="value">${this.formatIndicatorValue(data)}</span>
            </div>
            ${proximityInfo ? `
              <div class="threshold-proximity">
                <label>Next Threshold:</label>
                <span class="proximity">${proximityInfo}</span>
              </div>
            ` : ''}
          </div>

          <div class="trading-advice">
            <div class="advice-header">📊 Trading Intel:</div>
            <div class="advice-text ${tradingAdvice.class}">${tradingAdvice.text}</div>
            ${tradingAdvice.action ? `
              <div class="recommended-action">
                <strong>Recommendation:</strong> ${tradingAdvice.action}
              </div>
            ` : ''}
          </div>

          <div class="tooltip-footer">
            <div class="timestamp">🕒 ${timestamp}</div>
            <div class="data-source">📡 Live Data</div>
          </div>
        </div>
      </div>
    `;
  }

  analyzeSignalStrength(element, data) {
    let signalClass = 'neutral';
    let icon = '⚪';
    let text = 'NEUTRAL';
    let confidence = 50;

    // Analyze element classes for signal type
    if (element.classList.contains('degen-buy')) {
      signalClass = 'strong-buy';
      icon = '🟢';
      text = 'STRONG BUY';
      confidence = 85 + Math.random() * 10;
    } else if (element.classList.contains('mild-buy')) {
      signalClass = 'mild-buy';
      icon = '🔵';
      text = 'MILD BUY';
      confidence = 65 + Math.random() * 15;
    } else if (element.classList.contains('degen-sell')) {
      signalClass = 'strong-sell';
      icon = '🔴';
      text = 'STRONG SELL';
      confidence = 85 + Math.random() * 10;
    } else if (element.classList.contains('mild-sell')) {
      signalClass = 'mild-sell';
      icon = '🟠';
      text = 'MILD SELL';
      confidence = 65 + Math.random() * 15;
    }

    return {
      class: signalClass,
      icon: icon,
      text: text,
      confidence: Math.round(confidence)
    };
  }

  calculateThresholdProximity(data, thresholds) {
    if (!data || !data.value || !thresholds) return null;

    const value = typeof data.value === 'object' ?
      (data.value.value || data.value.macd || 0) : data.value;

    // Find next threshold
    const thresholdLevels = [
      { name: 'Green', value: thresholds.green },
      { name: 'Blue', value: thresholds.blue },
      { name: 'Orange', value: thresholds.orange },
      { name: 'Red', value: thresholds.red }
    ].sort((a, b) => Math.abs(value - a.value) - Math.abs(value - b.value));

    const nearest = thresholdLevels[0];
    const distance = Math.abs(value - nearest.value);
    const percentage = ((distance / nearest.value) * 100).toFixed(1);

    return `${nearest.name} (${percentage}% away)`;
  }

  generateTradingAdvice(signalInfo, proximityInfo) {
    let advice = {
      class: 'neutral',
      text: 'Monitor for clearer signals',
      action: null
    };

    switch (signalInfo.class) {
      case 'strong-buy':
        advice = {
          class: 'strong-bullish',
          text: 'Strong bullish momentum detected. High probability upward movement.',
          action: 'Consider LONG position with proper risk management'
        };
        break;
      case 'mild-buy':
        advice = {
          class: 'mild-bullish',
          text: 'Moderate bullish signals. Cautious optimism warranted.',
          action: 'Wait for confirmation or small position entry'
        };
        break;
      case 'strong-sell':
        advice = {
          class: 'strong-bearish',
          text: 'Strong bearish momentum detected. High probability downward movement.',
          action: 'Consider SHORT position or exit LONG positions'
        };
        break;
      case 'mild-sell':
        advice = {
          class: 'mild-bearish',
          text: 'Moderate bearish signals. Exercise caution.',
          action: 'Reduce position size or wait for clearer direction'
        };
        break;
    }

    return advice;
  }

  formatIndicatorValue(data) {
    if (!data || data.value === undefined) return 'N/A';

    if (typeof data.value === 'object') {
      if (data.value.macd !== undefined) {
        return `MACD: ${data.value.macd.toFixed(2)}, Signal: ${data.value.signal.toFixed(2)}`;
      } else if (data.value.value !== undefined) {
        return data.value.value.toFixed(2);
      }
      return JSON.stringify(data.value);
    }

    return typeof data.value === 'number' ? data.value.toFixed(2) : data.value.toString();
  }

  generateActionableTooltip(indicator, timeframe, data, color) {
    const indicatorName = indicator.toUpperCase();
    const value = data?.value || 'N/A';

    // Get actual thresholds from global thresholds or enhanced threshold sliders
    const thresholds = this.getActualThresholds(indicator);

    // Determine signal strength and action based on actual thresholds
    let action = 'NEUTRAL';
    let strength = 'Weak';
    let nextThreshold = 'N/A';
    let distance = 'N/A';

    if (data?.value !== undefined && data.value !== null) {
      const val = parseFloat(data.value);

      if (val >= thresholds.red) {
        action = 'STRONG SELL';
        strength = 'Strong';
        nextThreshold = `${thresholds.red} (Red Zone)`;
        distance = `${(val - thresholds.red).toFixed(1)} points above red`;
      } else if (val >= thresholds.orange) {
        action = 'WEAK SELL';
        strength = 'Mild';
        nextThreshold = `${thresholds.red} (Strong Sell)`;
        distance = `${(thresholds.red - val).toFixed(1)} points to strong sell`;
      } else if (val <= thresholds.green) {
        action = 'STRONG BUY';
        strength = 'Strong';
        nextThreshold = `${thresholds.green} (Green Zone)`;
        distance = `${(thresholds.green - val).toFixed(1)} points below green`;
      } else if (val <= thresholds.blue) {
        action = 'WEAK BUY';
        strength = 'Mild';
        nextThreshold = `${thresholds.green} (Strong Buy)`;
        distance = `${(val - thresholds.green).toFixed(1)} points to strong buy`;
      } else {
        action = 'NEUTRAL';
        strength = 'Neutral';
        nextThreshold = `${thresholds.blue} (Buy) / ${thresholds.orange} (Sell)`;
        distance = `${Math.min(val - thresholds.blue, thresholds.orange - val).toFixed(1)} points to signal`;
      }
    }

    // MACD analysis
    if (indicator === 'macd') {
        if (data?.histogram > 0.001) {
          action = 'STRONG BUY';
          strength = 'Strong';
        } else if (data?.histogram > 0) {
          action = 'WEAK BUY';
          strength = 'Mild';
        } else if (data?.histogram < -0.001) {
          action = 'STRONG SELL';
          strength = 'Strong';
        } else if (data?.histogram < 0) {
          action = 'WEAK SELL';
          strength = 'Mild';
        }
    } else {
        // Generic logic for other indicators
        if (data?.value > 0.6) {
          action = 'STRONG BUY';
          strength = 'Strong';
        } else if (data?.value > 0.2) {
          action = 'WEAK BUY';
          strength = 'Mild';
        } else if (data?.value < -0.6) {
          action = 'STRONG SELL';
          strength = 'Strong';
        } else if (data?.value < -0.2) {
          action = 'WEAK SELL';
          strength = 'Mild';
        }
    }

    return `
      <div class="enhanced-tooltip-content" style="
        background: rgba(10, 10, 30, 0.95);
        color: #00FFFF;
        padding: 12px;
        border-radius: 8px;
        border: 2px solid #00FFFF;
        box-shadow: 0 0 20px rgba(0, 255, 255, 0.5);
        font-family: 'Orbitron', monospace;
        min-width: 200px;
      ">
        <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
          <span style="font-weight: bold; color: #00FFFF;">${indicatorName}</span>
          <span style="background: rgba(0, 255, 255, 0.2); padding: 2px 6px; border-radius: 3px; font-size: 0.8em;">${timeframe}</span>
        </div>
        <div style="margin-bottom: 6px;">
          <span style="color: #888;">Value:</span>
          <span style="color: #FFF; font-weight: bold; margin-left: 8px;">${typeof value === 'number' ? value.toFixed(4) : value}</span>
        </div>
        <div style="margin-bottom: 6px;">
          <span style="color: #888;">Action:</span>
          <span style="color: ${action.includes('BUY') ? '#00FF00' : action.includes('SELL') ? '#FF0000' : '#FFA500'}; font-weight: bold; margin-left: 8px;">${action}</span>
        </div>
        <div style="margin-bottom: 6px;">
          <span style="color: #888;">Strength:</span>
          <span style="color: #FFF; margin-left: 8px;">${strength}</span>
        </div>
        ${nextThreshold !== 'N/A' ? `
        <div style="margin-bottom: 6px;">
          <span style="color: #888;">Next Threshold:</span>
          <span style="color: #FFF; margin-left: 8px;">${nextThreshold}</span>
        </div>
        <div style="margin-bottom: 6px;">
          <span style="color: #888;">Distance:</span>
          <span style="color: #FFF; margin-left: 8px;">${distance}</span>
        </div>
        ` : ''}
        <div style="font-size: 0.7em; color: #666; margin-top: 8px; border-top: 1px solid #333; padding-top: 4px;">
          Last Update: ${data?.timestamp ? new Date(data.timestamp).toLocaleTimeString() : 'N/A'}
        </div>
      </div>
    `;
  }

  hideEnhancedTooltip() {
    const tooltip = document.getElementById('global-tooltip');
    if (tooltip) {
      tooltip.style.display = 'none';
      tooltip.style.opacity = '0';
    }
  }

  getActualThresholds(indicator) {
    // Try to get thresholds from enhanced threshold sliders first
    if (window.enhancedThresholdSliders && window.enhancedThresholdSliders.defaultThresholds) {
      const enhanced = window.enhancedThresholdSliders.defaultThresholds[indicator];
      if (enhanced) return enhanced;
    }

    // Try to get from global thresholds object
    if (window.thresholds && window.thresholds[indicator]) {
      return window.thresholds[indicator];
    }

    // Fallback to default thresholds
    const defaults = {
      rsi: { green: 20, blue: 40, orange: 60, red: 80 },
      macd: { green: 15, blue: 35, orange: 65, red: 85 },
      stochRsi: { green: 20, blue: 40, orange: 60, red: 80 },
      bollingerBands: { green: 5, blue: 25, orange: 75, red: 95 },
      atr: { green: 10, blue: 30, orange: 70, red: 90 },
      volume: { green: 20, blue: 40, orange: 60, red: 80 },
      mfi: { green: 20, blue: 40, orange: 60, red: 80 },
      williamsR: { green: 80, blue: 60, orange: 40, red: 20 }, // Inverted
      cci: { green: 15, blue: 35, orange: 65, red: 85 },
      ultimateOscillator: { green: 20, blue: 40, orange: 60, red: 80 },
      adx: { green: 15, blue: 35, orange: 65, red: 85 }
    };

    return defaults[indicator] || { green: 20, blue: 40, orange: 60, red: 80 };
  }

  positionTooltip(tooltip, event) {
    const x = event.clientX + 15;
    const y = event.clientY - 10;

    // Ensure tooltip stays within viewport
    const rect = tooltip.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;

    let finalX = x;
    let finalY = y;

    if (x + rect.width > viewportWidth) {
      finalX = event.clientX - rect.width - 15;
    }

    if (y + rect.height > viewportHeight) {
      finalY = event.clientY - rect.height - 15;
    }

    tooltip.style.left = `${finalX}px`;
    tooltip.style.top = `${finalY}px`;
  }

  removeExistingTooltipListeners() {
    // Clone and replace elements to remove all event listeners
    const elementsWithTooltips = document.querySelectorAll('[data-tooltip]');
    elementsWithTooltips.forEach(element => {
      // Remove any existing mouseover/mouseout listeners by cloning
      const newElement = element.cloneNode(true);
      element.parentNode.replaceChild(newElement, element);
    });
  }

  addEnhancedTooltipListeners(tooltip) {
    // Use event delegation for better performance and to handle dynamic elements
    document.addEventListener('mouseover', (e) => {
      const target = e.target.closest('[data-tooltip]');
      if (target) {
        this.showTooltip(target, tooltip, e);
      }
    });

    document.addEventListener('mouseout', (e) => {
      const target = e.target.closest('[data-tooltip]');
      if (target) {
        this.hideTooltip(tooltip);
      }
    });

    document.addEventListener('mousemove', (e) => {
      const target = e.target.closest('[data-tooltip]');
      if (target && tooltip.classList.contains('visible')) {
        this.positionTooltip(tooltip, e);
      }
    });

    // Hide tooltip when scrolling or clicking
    document.addEventListener('scroll', () => {
      this.hideTooltip(tooltip);
    });

    document.addEventListener('click', () => {
      this.hideTooltip(tooltip);
    });
  }

  showTooltip(element, tooltip, event) {
    // Clear any existing timeout
    if (this.tooltipTimeout) {
      clearTimeout(this.tooltipTimeout);
    }

    // 🕒 ENHANCED TOOLTIP WITH TIMESTAMP AND INDICATOR DESCRIPTIONS
    const indicator = element.dataset.indicator;
    const timeframe = element.dataset.timeframe;

    if (indicator && timeframe) {
      // Get last update timestamp for 5-color logic
      const lastUpdateTime = this.getLastUpdateTimestamp(indicator, timeframe);

      // Get indicator description for educational tooltips
      const indicatorDescription = this.getIndicatorDescription(indicator);

      // Add timestamp and description to tooltip data
      if (!element.dataset.tooltip.includes('Last Updated:')) {
        const originalTooltip = element.dataset.tooltip;
        element.dataset.tooltip = `${originalTooltip}\n\n📚 ${indicatorDescription}\n\n🕒 Last Updated: ${lastUpdateTime.toLocaleString()}`;
      }
    }

    const tooltipText = element.getAttribute('data-tooltip');
    if (!tooltipText || tooltipText.trim() === '') return;

    // Enhanced tooltip content for signal lights
    let enhancedContent = tooltipText;
    
    if (element.classList.contains('signal-circle') || element.classList.contains('circle')) {
      const indicator = element.getAttribute('data-ind');
      const timeframe = element.getAttribute('data-tf');
      const color = element.style.backgroundColor || '#808080';
      
      if (indicator && timeframe) {
        // Get current indicator data for enhanced tooltip
        const indicatorData = window.indicatorsData?.[timeframe]?.[indicator];
        if (indicatorData) {
          enhancedContent = this.generateEnhancedTooltip(indicator, timeframe, indicatorData, color);
        }
      }
    }

    tooltip.innerHTML = enhancedContent;
    tooltip.classList.add('visible');
    tooltip.style.opacity = '1';
    
    this.positionTooltip(tooltip, event);
  }

  hideTooltip(tooltip) {
    // Add small delay to prevent flickering
    this.tooltipTimeout = setTimeout(() => {
      tooltip.style.opacity = '0';
      tooltip.classList.remove('visible');
    }, 100);
  }

  positionTooltip(tooltip, event) {
    const tooltipRect = tooltip.getBoundingClientRect();
    const viewportWidth = window.innerWidth;
    const viewportHeight = window.innerHeight;
    
    let x = event.clientX + 15;
    let y = event.clientY - 10;
    
    // Adjust if tooltip would go off screen
    if (x + tooltipRect.width > viewportWidth) {
      x = event.clientX - tooltipRect.width - 15;
    }
    
    if (y + tooltipRect.height > viewportHeight) {
      y = event.clientY - tooltipRect.height - 10;
    }
    
    // Ensure tooltip doesn't go above viewport
    if (y < 0) {
      y = event.clientY + 15;
    }
    
    // Ensure tooltip doesn't go left of viewport
    if (x < 0) {
      x = 10;
    }
    
    tooltip.style.left = `${x}px`;
    tooltip.style.top = `${y}px`;
  }

  generateEnhancedTooltip(indicator, timeframe, data, color) {
    const colorName = this.getColorName(color);
    const actionableSignal = this.getActionableSignal(indicator, data, colorName);
    const thresholdInfo = this.getThresholdDistance(indicator, data, colorName);

    let content = `
      <div class="tooltip-header" style="background: linear-gradient(45deg, ${color}, rgba(255,255,255,0.1)); padding: 8px; border-radius: 4px; margin-bottom: 8px;">
        <div style="font-weight: bold; font-size: 1.1rem;">${indicator.toUpperCase()} (${timeframe})</div>
        <div style="font-size: 0.9rem; color: #cccccc;">Current Value: ${typeof data.value === 'number' ? data.value.toFixed(2) : data.value || 'N/A'}</div>
      </div>

      <div class="tooltip-signal" style="margin-bottom: 8px; padding: 6px; background: rgba(0,0,0,0.3); border-radius: 4px; border-left: 3px solid ${this.getSignalColor(actionableSignal.type)};">
        <div style="font-size: 1rem; font-weight: bold; color: ${this.getSignalColor(actionableSignal.type)};">
          🎯 ${actionableSignal.signal}
        </div>
        <div style="font-size: 0.9rem; color: #aaaaaa; margin: 4px 0;">
          ${actionableSignal.description}
        </div>
        <div style="font-size: 0.9rem; font-weight: bold; color: #ffffff; background: rgba(0,0,0,0.5); padding: 4px; border-radius: 3px;">
          ${actionableSignal.action}
        </div>
        <div style="font-size: 0.8rem; color: #00ff00; margin-top: 4px;">
          📈 Confidence: ${actionableSignal.confidence}%
        </div>
      </div>

      <div class="tooltip-threshold" style="margin-bottom: 8px; padding: 6px; background: rgba(0,255,255,0.1); border-radius: 3px;">
        <div style="font-size: 0.9rem; color: #00ffff;">
          📊 ${thresholdInfo.message}
        </div>
        <div style="font-size: 0.8rem; color: #cccccc;">
          ${thresholdInfo.tip}
        </div>
      </div>

      <div class="tooltip-usage" style="font-size: 0.8rem; color: #ffff00; font-style: italic; padding: 4px; background: rgba(255,255,0,0.1); border-radius: 3px;">
        💡 ${this.getUsageTip(indicator, actionableSignal.type)}
      </div>
    `;

    return content;
    
    // Add interpretation based on indicator type
    const interpretation = this.getIndicatorInterpretation(indicator, data, colorName);
    if (interpretation) {
      content += `<div class="tooltip-interpretation">${interpretation}</div>`;
    }
    
    return content;
  }

  // Get confidence level based on indicator and data
  getConfidenceLevel(indicator, data) {
    if (!data) return 0;

    switch (indicator.toLowerCase()) {
      case 'rsi':
        const rsi = data.value || data.rsi || 0;
        if (rsi > 80 || rsi < 20) return 85;
        if (rsi > 70 || rsi < 30) return 70;
        return 45;

      case 'macd':
        const histogram = data.histogram || 0;
        if (Math.abs(histogram) > 0.001) return 80;
        if (Math.abs(histogram) > 0.0005) return 65;
        return 40;

      case 'stochastic':
        const k = data.k || data.value || 0;
        if (k > 80 || k < 20) return 75;
        return 50;

      default:
        return 60;
    }
  }

  // Get risk level based on action
  getRiskLevel(action) {
    switch (action.toLowerCase()) {
      case 'strong buy':
      case 'strong sell':
        return 'high';
      case 'weak buy':
      case 'weak sell':
        return 'medium';
      default:
        return 'low';
    }
  }

  // Get trading advice
  getTradingAdvice(indicator, data, colorName, actionableSignal) {
    const advice = [];

    if (actionableSignal.type.includes('buy')) {
      advice.push('💡 Consider entry with stop-loss');
      advice.push('📈 Watch for confirmation signals');
    } else if (actionableSignal.type.includes('sell')) {
      advice.push('💡 Consider taking profits');
      advice.push('📉 Watch for reversal patterns');
    } else {
      advice.push('💡 Wait for clearer signals');
      advice.push('📊 Monitor for breakout');
    }

    return advice.join('<br>');
  }

  // 🎯 ACTIONABLE SIGNAL ANALYSIS - USES ACTUAL 5-COLOR LOGIC THRESHOLDS
  getActionableSignal(indicator, data, colorName) {
    const value = typeof data.value === 'number' ? data.value : parseFloat(data.value) || 50;

    // Get actual thresholds from the global thresholds object
    const thresholds = window.thresholds?.[indicator] || {
      green: 20, blue: 40, orange: 60, red: 80
    };

    // Determine signal based on actual 5-color logic
    let signalInfo = this.determineSignalFromColor(colorName, value, thresholds, indicator);

    // Get threshold distances for enhanced signals
    const thresholdInfo = this.getThresholdDistance(indicator, data, colorName, thresholds);

    // Override with specific logic for known indicators
    switch (indicator.toLowerCase()) {
      case 'rsi':
        if (value >= thresholds.red) return {
          type: 'strong_sell',
          signal: 'STRONG SELL',
          description: 'Extremely overbought - high probability reversal',
          action: '🔴 Take profits or short',
          confidence: 85,
          thresholdDistance: thresholdInfo
        };
        if (value >= thresholds.orange) return {
          type: 'weak_sell',
          signal: 'WEAK SELL',
          description: 'Overbought - consider taking profits',
          action: '🟠 Reduce position size',
          confidence: 65,
          thresholdDistance: thresholdInfo
        };
        if (value <= thresholds.green) return {
          type: 'strong_buy',
          signal: 'STRONG BUY',
          description: 'Extremely oversold - high probability bounce',
          action: '🟢 Buy the dip aggressively',
          confidence: 85,
          thresholdDistance: thresholdInfo
        };
        if (value <= thresholds.blue) return {
          type: 'weak_buy',
          signal: 'WEAK BUY',
          description: 'Oversold - potential buying opportunity',
          action: '🔵 Scale into position',
          confidence: 65,
          thresholdDistance: thresholdInfo
        };
        return {
          type: 'neutral',
          signal: 'NEUTRAL',
          description: 'RSI in normal range - wait for clearer signal',
          action: '⚪ Monitor for breakout',
          confidence: 50,
          thresholdDistance: thresholdInfo
        };

      case 'stochrsi':
        if (value >= 0.8) return {
          type: 'strong_sell',
          signal: 'STRONG SELL',
          description: 'Stochastic overbought - momentum turning',
          action: '🔴 Exit longs, consider shorts',
          confidence: 80,
          thresholdDistance: thresholdInfo
        };
        if (value >= 0.7) return {
          type: 'weak_sell',
          signal: 'WEAK SELL',
          description: 'Approaching overbought - caution advised',
          action: '🟠 Prepare to take profits',
          confidence: 60,
          thresholdDistance: thresholdInfo
        };
        if (value <= 0.2) return {
          type: 'strong_buy',
          signal: 'STRONG BUY',
          description: 'Stochastic oversold - momentum building',
          action: '🟢 Enter long positions',
          confidence: 80,
          thresholdDistance: thresholdInfo
        };
        if (value <= 0.3) return {
          type: 'weak_buy',
          signal: 'WEAK BUY',
          description: 'Approaching oversold - watch for entry',
          action: '🔵 Prepare to buy',
          confidence: 60,
          thresholdDistance: thresholdInfo
        };
        return {
          type: 'neutral',
          signal: 'NEUTRAL',
          description: 'Stochastic in middle range - no clear signal',
          action: '⚪ Wait for extremes',
          confidence: 50,
          thresholdDistance: thresholdInfo
        };

      case 'macd':
        if (value > 0 && Math.abs(value) > 100) return {
          type: 'strong_buy',
          signal: 'STRONG BUY',
          description: 'Strong bullish momentum - trend confirmed',
          action: '🟢 Enter long positions',
          confidence: 85,
          thresholdDistance: thresholdInfo
        };
        if (value > 0) return {
          type: 'weak_buy',
          signal: 'WEAK BUY',
          description: 'Bullish momentum - trend developing',
          action: '🔵 Consider long entry',
          confidence: 65,
          thresholdDistance: thresholdInfo
        };
        if (value < 0 && Math.abs(value) > 100) return {
          type: 'strong_sell',
          signal: 'STRONG SELL',
          description: 'Strong bearish momentum - downtrend confirmed',
          action: '🔴 Enter short positions',
          confidence: 85,
          thresholdDistance: thresholdInfo
        };
        if (value < 0) return {
          type: 'weak_sell',
          signal: 'WEAK SELL',
          description: 'Bearish momentum - downtrend developing',
          action: '🟠 Consider short entry',
          confidence: 65,
          thresholdDistance: thresholdInfo
        };
        return {
          type: 'neutral',
          signal: 'NEUTRAL',
          description: 'MACD near zero - momentum unclear',
          action: '⚪ Wait for signal',
          confidence: 50,
          thresholdDistance: thresholdInfo
        };

      case 'atr':
        if (value > 3) return {
          type: 'caution',
          signal: 'HIGH VOLATILITY',
          description: 'Extreme volatility - use tight stops',
          action: '⚠️ Reduce position size',
          confidence: 75,
          thresholdDistance: thresholdInfo
        };
        if (value > 2) return {
          type: 'moderate',
          signal: 'MODERATE VOLATILITY',
          description: 'Increased volatility - adjust position size',
          action: '📊 Use wider stops',
          confidence: 60,
          thresholdDistance: thresholdInfo
        };
        return {
          type: 'low',
          signal: 'LOW VOLATILITY',
          description: 'Low volatility - potential breakout coming',
          action: '🎯 Prepare for breakout',
          confidence: 55,
          thresholdDistance: thresholdInfo
        };

      default:
        return {
          type: 'neutral',
          signal: 'MONITOR',
          description: 'Monitor for signal development',
          action: '👀 Wait and watch',
          confidence: 50,
          thresholdDistance: thresholdInfo
        };
    }
  }

  // Helper function to determine signal from actual color
  determineSignalFromColor(colorName, value, thresholds, indicator) {
    switch (colorName) {
      case 'green':
        return {
          type: 'strong_buy',
          signal: 'STRONG BUY',
          description: `${indicator.toUpperCase()} shows strong buy signal`,
          action: '🟢 Enter long positions',
          confidence: 85
        };
      case 'blue':
        return {
          type: 'weak_buy',
          signal: 'WEAK BUY',
          description: `${indicator.toUpperCase()} shows mild buy signal`,
          action: '🔵 Consider long entry',
          confidence: 65
        };
      case 'grey':
        return {
          type: 'neutral',
          signal: 'NEUTRAL',
          description: `${indicator.toUpperCase()} in neutral zone`,
          action: '⚪ Wait for clearer signal',
          confidence: 50
        };
      case 'orange':
        return {
          type: 'weak_sell',
          signal: 'WEAK SELL',
          description: `${indicator.toUpperCase()} shows mild sell signal`,
          action: '🟠 Consider taking profits',
          confidence: 65
        };
      case 'red':
        return {
          type: 'strong_sell',
          signal: 'STRONG SELL',
          description: `${indicator.toUpperCase()} shows strong sell signal`,
          action: '🔴 Exit longs or short',
          confidence: 85
        };
      default:
        return {
          type: 'neutral',
          signal: 'UNKNOWN',
          description: 'Signal unclear',
          action: '❓ Monitor closely',
          confidence: 30
        };
    }
  }

  getThresholdDistance(indicator, data, colorName, thresholds = null) {
    const value = typeof data.value === 'number' ? data.value : parseFloat(data.value) || 50;

    // Use actual thresholds if provided, otherwise get from global
    const actualThresholds = thresholds || window.thresholds?.[indicator] || {
      green: 20, blue: 40, orange: 60, red: 80
    };

    // Calculate distances to each threshold
    const distanceToGreen = Math.abs(value - actualThresholds.green);
    const distanceToBlue = Math.abs(value - actualThresholds.blue);
    const distanceToOrange = Math.abs(value - actualThresholds.orange);
    const distanceToRed = Math.abs(value - actualThresholds.red);

    // Determine next threshold based on current color
    switch (colorName) {
      case 'green':
        return {
          message: `Strong buy zone (${actualThresholds.green}% threshold)`,
          tip: `${distanceToBlue.toFixed(1)} points to mild buy zone`
        };
      case 'blue':
        return {
          message: `Mild buy zone (${actualThresholds.blue}% threshold)`,
          tip: `${distanceToGreen.toFixed(1)} points to strong buy, ${(actualThresholds.orange - value).toFixed(1)} to neutral`
        };
      case 'grey':
        const nextThreshold = value < 50 ? actualThresholds.blue : actualThresholds.orange;
        const nextZone = value < 50 ? 'mild buy' : 'mild sell';
        return {
          message: `Neutral zone (between ${actualThresholds.blue}% and ${actualThresholds.orange}%)`,
          tip: `${Math.abs(value - nextThreshold).toFixed(1)} points to ${nextZone} zone`
        };
      case 'orange':
        return {
          message: `Mild sell zone (${actualThresholds.orange}% threshold)`,
          tip: `${(value - actualThresholds.orange).toFixed(1)} points above neutral, ${(actualThresholds.red - value).toFixed(1)} to strong sell`
        };
      case 'red':
        return {
          message: `Strong sell zone (${actualThresholds.red}% threshold)`,
          tip: `${(value - actualThresholds.red).toFixed(1)} points above strong sell threshold`
        };
      default:
        return {
          message: `Current: ${value.toFixed(2)}`,
          tip: 'Monitor for threshold breaks'
        };
    }
  }

  getUsageTip(indicator, signalType) {
    const tips = {
      'rsi': {
        'strong_buy': 'Best used with support levels - wait for confirmation',
        'weak_buy': 'Good for DCA entries - scale in gradually',
        'strong_sell': 'Take profits or short - high probability reversal',
        'weak_sell': 'Reduce position size - prepare for potential reversal',
        'neutral': 'Wait for RSI to reach extreme levels for clear signals'
      },
      'macd': {
        'strong_buy': 'Trend following signal - ride the momentum',
        'weak_buy': 'Early trend signal - good for swing trades',
        'strong_sell': 'Strong reversal signal - consider shorts',
        'weak_sell': 'Momentum weakening - tighten stops',
        'neutral': 'Watch for MACD line crossovers for direction'
      },
      'stochrsi': {
        'strong_buy': 'Fast reversal signal - good for scalping',
        'weak_buy': 'Early momentum signal - combine with other indicators',
        'strong_sell': 'Quick reversal expected - fast exit signal',
        'weak_sell': 'Momentum slowing - prepare for reversal',
        'neutral': 'Wait for extreme readings for best signals'
      }
    };

    return tips[indicator.toLowerCase()]?.[signalType] || 'Combine with other indicators for confirmation';
  }

  getSignalColor(signalType) {
    const colors = {
      'strong_buy': '#00ff00',
      'weak_buy': '#88ff88',
      'neutral': '#ffff00',
      'weak_sell': '#ff8888',
      'strong_sell': '#ff0000',
      'caution': '#ff8800',
      'moderate': '#ffff88',
      'low': '#88ffff'
    };
    return colors[signalType] || '#ffffff';
  }

  getColorName(color) {
    const rgb = color.toLowerCase();

    // More flexible color detection for various red shades
    if (rgb.includes('255, 0, 0') || rgb.includes('#ff0000') || rgb.includes('red') ||
        rgb.includes('#ff4444') || rgb.includes('255, 68, 68') ||
        (rgb.includes('rgb') && rgb.includes('255') && rgb.includes('0') && rgb.includes('0'))) {
      return 'red';
    }

    // Orange/yellow shades for mild sell
    if (rgb.includes('255, 165, 0') || rgb.includes('#ffa500') || rgb.includes('orange') ||
        rgb.includes('#ffaa00') || rgb.includes('255, 170, 0')) {
      return 'orange';
    }

    // Gray/neutral shades
    if (rgb.includes('128, 128, 128') || rgb.includes('#808080') || rgb.includes('gray') ||
        rgb.includes('grey') || rgb.includes('#888') || rgb.includes('#999')) {
      return 'grey';
    }

    // Blue shades for mild buy
    if (rgb.includes('0, 0, 255') || rgb.includes('#0000ff') || rgb.includes('blue') ||
        rgb.includes('#00aaff') || rgb.includes('0, 170, 255') ||
        rgb.includes('#4444ff') || rgb.includes('68, 68, 255')) {
      return 'blue';
    }

    // Green shades for strong buy
    if (rgb.includes('0, 255, 0') || rgb.includes('#00ff00') || rgb.includes('green') ||
        rgb.includes('#44ff44') || rgb.includes('68, 255, 68') ||
        (rgb.includes('rgb') && rgb.includes('0') && rgb.includes('255') && rgb.includes('0'))) {
      return 'green';
    }

    return 'neutral';
  }

  getSignalStrength(colorName) {
    switch (colorName) {
      case 'red': return 'Strong Sell Signal';
      case 'orange': return 'Mild Sell Signal';
      case 'grey': return 'Neutral Signal';
      case 'blue': return 'Mild Buy Signal';
      case 'green': return 'Strong Buy Signal';
      default: return 'Unknown Signal';
    }
  }

  getIndicatorInterpretation(indicator, data, colorName) {
    switch (indicator) {
      case 'rsi':
        if (data.value > 70) return 'Overbought - potential sell opportunity';
        if (data.value < 30) return 'Oversold - potential buy opportunity';
        return 'Neutral territory';
        
      case 'macd':
        if (data.macd > data.signal) return 'Bullish momentum';
        if (data.macd < data.signal) return 'Bearish momentum';
        return 'Momentum neutral';
        
      case 'bollingerBands':
        if (data.position > 80) return 'Price near upper band - resistance level';
        if (data.position < 20) return 'Price near lower band - support level';
        return 'Price in middle range';
        
      case 'volume':
        if (colorName === 'green' || colorName === 'blue') return 'High volume - strong interest';
        if (colorName === 'red' || colorName === 'orange') return 'Low volume - weak interest';
        return 'Average volume';
        
      default:
        return null;
    }
  }

  applyTooltipStyles() {
    const style = document.createElement('style');
    style.textContent = `
      /* Enhanced Tooltip Styles */
      .enhanced-tooltip {
        position: fixed;
        background: rgba(0, 20, 40, 0.95);
        border: 2px solid rgba(0, 255, 255, 0.6);
        border-radius: 8px;
        padding: 8px 12px;
        color: #ffffff;
        font-family: 'Orbitron', sans-serif;
        font-size: 12px;
        max-width: 250px;
        z-index: 999999 !important; /* Ensure it's above everything */
        pointer-events: none;
        opacity: 0;
        transition: opacity 0.2s ease;
        backdrop-filter: blur(10px);
        box-shadow: 0 8px 32px rgba(0, 255, 255, 0.3);
      }
      
      .enhanced-tooltip.visible {
        opacity: 1;
      }
      
      .tooltip-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
        border-bottom: 1px solid rgba(0, 255, 255, 0.3);
        padding-bottom: 4px;
      }
      
      .tooltip-indicator {
        font-weight: bold;
        color: #00ffff;
        font-size: 13px;
      }
      
      .tooltip-timeframe {
        background: rgba(0, 255, 255, 0.2);
        padding: 2px 6px;
        border-radius: 4px;
        font-size: 10px;
        color: #00ffff;
      }
      
      .tooltip-signal {
        display: flex;
        align-items: center;
        margin: 4px 0;
      }
      
      .tooltip-color {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 6px;
        border: 1px solid rgba(255, 255, 255, 0.3);
      }
      
      .tooltip-signal-text {
        font-weight: bold;
        font-size: 11px;
      }
      
      .tooltip-value {
        color: #ffa502;
        font-size: 11px;
        margin: 2px 0;
      }
      
      .tooltip-interpretation {
        color: #96ceb4;
        font-size: 10px;
        font-style: italic;
        margin-top: 4px;
        border-top: 1px solid rgba(0, 255, 255, 0.2);
        padding-top: 4px;
      }
      
      /* Hide default browser tooltips */
      [title] {
        position: relative;
      }
      
      [title]:hover::after {
        display: none !important;
      }
      
      /* Ensure signal lights don't show default tooltips */
      .signal-circle[title],
      .circle[title] {
        title: none !important;
      }
      
      /* Fix z-index conflicts */
      .enhanced-tooltip {
        z-index: 999999 !important;
      }
      
      /* Ensure tooltip appears above light logic overlays */
      .light-logic-overlay {
        z-index: 1000 !important;
      }
      
      .enhanced-tooltip {
        z-index: 1001 !important;
      }
      /* Splendid Tooltip Styling */
      .splendid-tooltip {
        background: linear-gradient(135deg, rgba(0, 20, 40, 0.95), rgba(0, 40, 80, 0.95));
        border: 2px solid rgba(0, 255, 255, 0.5);
        border-radius: 12px;
        padding: 0;
        min-width: 280px;
        max-width: 350px;
        box-shadow: 0 8px 32px rgba(0, 255, 255, 0.3);
        backdrop-filter: blur(10px);
        font-family: 'Roboto', sans-serif;
      }

      .tooltip-header {
        background: linear-gradient(90deg, rgba(0, 255, 255, 0.2), rgba(0, 128, 255, 0.2));
        padding: 12px 15px;
        border-radius: 10px 10px 0 0;
        border-bottom: 1px solid rgba(0, 255, 255, 0.3);
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .indicator-title {
        font-size: 1.1rem;
        font-weight: bold;
        color: #00FFFF;
        text-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
      }

      .timeframe-badge {
        background: rgba(0, 255, 255, 0.3);
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.8rem;
        font-weight: bold;
        color: #FFFFFF;
      }

      .tooltip-body {
        padding: 15px;
      }

      .signal-info {
        margin-bottom: 12px;
        text-align: center;
      }

      .signal-strength {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 8px;
        margin-bottom: 6px;
      }

      .signal-strength.strong-buy { color: #00FF00; }
      .signal-strength.mild-buy { color: #0080FF; }
      .signal-strength.strong-sell { color: #FF0000; }
      .signal-strength.mild-sell { color: #FFA500; }
      .signal-strength.neutral { color: #808080; }

      .signal-icon {
        font-size: 1.2rem;
      }

      .signal-text {
        font-weight: bold;
        font-size: 1rem;
      }

      .signal-confidence {
        font-size: 0.9rem;
        color: #CCCCCC;
      }

      .confidence-value {
        color: #00FFFF;
        font-weight: bold;
      }

      .data-section {
        background: rgba(0, 0, 0, 0.3);
        padding: 10px;
        border-radius: 6px;
        margin: 12px 0;
      }

      .data-section label {
        color: #AAAAAA;
        font-size: 0.8rem;
        display: inline-block;
        min-width: 80px;
      }

      .data-section .value,
      .data-section .proximity {
        color: #FFFFFF;
        font-weight: bold;
        margin-left: 8px;
      }

      .trading-advice {
        background: rgba(0, 40, 80, 0.4);
        padding: 12px;
        border-radius: 6px;
        border-left: 4px solid #00FFFF;
        margin: 12px 0;
      }

      .advice-header {
        font-weight: bold;
        color: #00FFFF;
        margin-bottom: 6px;
        font-size: 0.9rem;
      }

      .advice-text {
        font-size: 0.9rem;
        line-height: 1.4;
        margin-bottom: 8px;
      }

      .advice-text.strong-bullish { color: #00FF00; }
      .advice-text.mild-bullish { color: #88FF88; }
      .advice-text.strong-bearish { color: #FF0000; }
      .advice-text.mild-bearish { color: #FF8888; }
      .advice-text.neutral { color: #CCCCCC; }

      .recommended-action {
        background: rgba(0, 255, 255, 0.1);
        padding: 6px 8px;
        border-radius: 4px;
        font-size: 0.8rem;
        border: 1px solid rgba(0, 255, 255, 0.3);
      }

      .tooltip-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding-top: 10px;
        border-top: 1px solid rgba(0, 255, 255, 0.2);
        font-size: 0.7rem;
        color: #888888;
      }

      .enhanced-tooltip {
        animation: tooltipFadeIn 0.3s ease-out;
      }

      @keyframes tooltipFadeIn {
        from {
          opacity: 0;
          transform: scale(0.9) translateY(10px);
        }
        to {
          opacity: 1;
          transform: scale(1) translateY(0);
        }
      }
    `;
    document.head.appendChild(style);
  }

  // Method to initialize tooltips for individual signal circles
  initializeSignalCircleTooltips(specificCircle = null) {
    const circles = specificCircle ? [specificCircle] : document.querySelectorAll('.signal-circle');

    circles.forEach(circle => {
      // Remove existing listeners to prevent duplicates
      const existingHandler = circle._tooltipHandler;
      if (existingHandler) {
        circle.removeEventListener('mouseenter', existingHandler);
        circle.removeEventListener('mouseleave', this.hideEnhancedTooltip);
      }

      // Create new handler
      const handler = (e) => this.showEnhancedSignalTooltip(circle, e);
      circle._tooltipHandler = handler;

      // Add enhanced tooltip listeners
      circle.addEventListener('mouseenter', handler);
      circle.addEventListener('mouseleave', () => this.hideEnhancedTooltip());
    });

    console.log(`[TooltipFixes] Initialized enhanced tooltips for ${circles.length} signal circles`);
  }

  cleanup() {
    console.log('[TooltipFixes] Cleaning up tooltip system...');

    // Remove all tooltip event listeners
    const circles = document.querySelectorAll('.signal-circle');
    circles.forEach(circle => {
      const handler = circle._tooltipHandler;
      if (handler) {
        circle.removeEventListener('mouseenter', handler);
        circle.removeEventListener('mouseleave', this.hideEnhancedTooltip);
        delete circle._tooltipHandler;
      }
    });

    // Hide any visible tooltips
    this.hideEnhancedTooltip();

    console.log('[TooltipFixes] Cleanup complete');
  }

  formatIndicatorValue(data) {
    if (!data) return 'No data available';

    if (data.value !== undefined && data.value !== null) {
      if (typeof data.value === 'number') {
        return data.value.toFixed(4);
      }
      return data.value.toString();
    }

    if (data.signal) {
      return `Signal: ${data.signal}`;
    }

    return 'Loading...';
  }

  // 🕒 GET LAST UPDATE TIMESTAMP FOR 5-COLOR LOGIC
  getLastUpdateTimestamp(indicator, timeframe) {
    try {
      // 🎯 PRIORITY 1: Check WebSocket received data timestamps
      if (window.wsDataTimestamps && window.wsDataTimestamps[timeframe] && window.wsDataTimestamps[timeframe][indicator]) {
        return new Date(window.wsDataTimestamps[timeframe][indicator]);
      }

      // 🎯 PRIORITY 2: Check stored update times from signal system
      if (window.signalUpdateTimes && window.signalUpdateTimes[indicator] && window.signalUpdateTimes[indicator][timeframe]) {
        return new Date(window.signalUpdateTimes[indicator][timeframe]);
      }

      // 🎯 PRIORITY 3: Check indicator data for timestamp (both possible variable names)
      if (window.indicatorsData && window.indicatorsData[timeframe] && window.indicatorsData[timeframe][indicator]) {
        const data = window.indicatorsData[timeframe][indicator];
        if (data.timestamp) {
          return new Date(data.timestamp);
        }
        if (data.lastUpdate) {
          return new Date(data.lastUpdate);
        }
      }

      // Also check window.indicatorData (alternative naming)
      if (window.indicatorData && window.indicatorData[timeframe] && window.indicatorData[timeframe][indicator]) {
        const data = window.indicatorData[timeframe][indicator];
        if (data.timestamp) {
          return new Date(data.timestamp);
        }
        if (data.lastUpdate) {
          return new Date(data.lastUpdate);
        }
      }

      // 🎯 PRIORITY 4: Check timeframe-specific last update
      if (window.timeframeLastUpdate && window.timeframeLastUpdate[timeframe]) {
        return new Date(window.timeframeLastUpdate[timeframe]);
      }

      // 🎯 PRIORITY 5: Check last WebSocket update time
      if (window.lastDataUpdate) {
        return new Date(window.lastDataUpdate);
      }

      // 🎯 PRIORITY 6: Check WebSocket connection time
      if (window.wsConnectedAt) {
        return new Date(window.wsConnectedAt);
      }

      // ⚠️ FALLBACK: Return a clearly old timestamp to indicate no real data
      console.warn(`[TooltipFixes] ⚠️ No real timestamp found for ${indicator}-${timeframe}, using fallback`);
      return new Date(Date.now() - 300000); // 5 minutes ago to indicate stale data
    } catch (error) {
      console.error('[TooltipFixes] Error getting timestamp:', error);
      return new Date(Date.now() - 300000); // 5 minutes ago to indicate error
    }
  }

  // 📚 GET INDICATOR EDUCATIONAL DESCRIPTIONS
  getIndicatorDescription(indicator) {
    const descriptions = {
      'rsi': 'RSI (Relative Strength Index) measures momentum. Values above 70 suggest overbought conditions (potential sell), below 30 suggest oversold (potential buy).',
      'macd': 'MACD (Moving Average Convergence Divergence) shows trend changes. When MACD line crosses above signal line, it suggests bullish momentum.',
      'bb': 'Bollinger Bands show volatility. Price touching upper band suggests overbought, lower band suggests oversold. Squeeze indicates low volatility.',
      'ema': 'EMA (Exponential Moving Average) gives more weight to recent prices. Price above EMA suggests uptrend, below suggests downtrend.',
      'sma': 'SMA (Simple Moving Average) smooths price data. Acts as support/resistance. Price above SMA is bullish, below is bearish.',
      'atr': 'ATR (Average True Range) measures volatility. High ATR means high volatility (larger price swings), low ATR means stability.',
      'volume': 'Volume shows trading activity. High volume confirms price moves. Volume spikes often precede significant price changes.',
      'stoch': 'Stochastic Oscillator compares closing price to price range. Above 80 is overbought, below 20 is oversold.',
      'adx': 'ADX (Average Directional Index) measures trend strength. Above 25 indicates strong trend, below 20 indicates weak trend.',
      'cci': 'CCI (Commodity Channel Index) identifies cyclical trends. Above +100 suggests overbought, below -100 suggests oversold.',
      'roc': 'ROC (Rate of Change) measures momentum. Positive values indicate upward momentum, negative values indicate downward momentum.',
      'williams': 'Williams %R measures overbought/oversold conditions. Values above -20 suggest overbought, below -80 suggest oversold.',
      'mfi': 'MFI (Money Flow Index) incorporates volume with price. Above 80 suggests overbought, below 20 suggests oversold conditions.',
      'obv': 'OBV (On-Balance Volume) relates volume to price changes. Rising OBV suggests accumulation, falling OBV suggests distribution.'
    };

    return descriptions[indicator.toLowerCase()] || `${indicator.toUpperCase()} is a technical indicator used for market analysis and trading decisions.`;
  }
}

// Initialize tooltip fixes with memory management
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    // Clean up any existing instance
    if (window.tooltipFixes) {
      window.tooltipFixes.cleanup();
    }

    window.tooltipFixes = new TooltipFixes();

    // Force initialize tooltips for existing signal circles
    setTimeout(() => {
      if (window.tooltipFixes) {
        window.tooltipFixes.initializeSignalCircleTooltips();
        console.log('[TooltipFixes] 🎯 Force-initialized tooltips for existing signal circles');
      }
    }, 2000);
  }, 1000);
});

// Cleanup on page unload to prevent memory leaks
window.addEventListener('beforeunload', () => {
  if (window.tooltipFixes) {
    window.tooltipFixes.cleanup();
  }
});

// Export for module use
if (typeof module !== 'undefined' && module.exports) {
  module.exports = TooltipFixes;
}
