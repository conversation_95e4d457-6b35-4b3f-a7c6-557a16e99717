// js/ui/unified-threshold-sliders.js
/**
 * StarCrypt Unified Threshold Sliders
 * 
 * Single, properly scaled threshold slider system with 4 thumbs and 5 color segments
 * Fixes all scaling, layering, and positioning issues
 */

(function() {
  'use strict';

  // Prevent multiple initializations
  if (window.unifiedThresholdSliders) {
    console.log('[UnifiedThresholds] Already initialized');
    return;
  }

  console.log('[UnifiedThresholds] Initializing unified threshold slider system...');

  // Main class for unified threshold sliders
  class UnifiedThresholdSliders {
    constructor() {
      this.activeThumb = null;
      this.isDragging = false;
      this.startX = 0;
      this.startValue = 0;
    }

    // Get proper display name for indicators
    getIndicatorDisplayName(indicatorName) {
      const displayNames = {
        rsi: 'RSI',
        stochRsi: 'Stoch RSI',
        macd: 'MACD',
        bollingerBands: 'Bollinger Bands',
        williamsR: 'Williams %R',
        ultimateOscillator: 'Ultimate Oscillator',
        mfi: 'Money Flow Index',
        cci: 'CCI',
        roc: 'Rate of Change',
        stochasticOscillator: 'Stochastic',
        adx: 'ADX',
        aroon: 'Aroon',
        trix: 'TRIX',
        chaikinMoneyFlow: 'Chaikin Money Flow',
        timeAnomaly: 'Time Anomaly'
      };
      return displayNames[indicatorName] || indicatorName.charAt(0).toUpperCase() + indicatorName.slice(1);
    }

    // Create a single unified threshold slider
    createUnifiedSlider(indicatorName, container) {
      const thresholds = window.thresholds[indicatorName];
      if (!thresholds) return null;

      // Create main slider container
      const sliderContainer = document.createElement('div');
      sliderContainer.className = 'unified-threshold-container';
      sliderContainer.dataset.indicator = indicatorName;
      sliderContainer.style.cssText = `
        position: relative;
        width: 100%;
        margin: 20px 0;
        padding: 15px;
        background: rgba(0, 20, 40, 0.8);
        border: 1px solid rgba(0, 255, 255, 0.3);
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      `;

      // Create indicator label
      const label = document.createElement('div');
      label.className = 'unified-slider-label';
      label.textContent = this.getIndicatorDisplayName(indicatorName);
      label.style.cssText = `
        color: #00FFFF;
        font-size: 14px;
        font-weight: bold;
        text-align: center;
        margin-bottom: 15px;
        text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
      `;

      // Create slider track with proper scaling
      const sliderTrack = document.createElement('div');
      sliderTrack.className = 'unified-slider-track';
      sliderTrack.style.cssText = `
        position: relative;
        width: 100%;
        height: 20px;
        background: #333;
        border-radius: 10px;
        border: 1px solid rgba(255, 255, 255, 0.2);
        overflow: visible;
        margin: 10px 0;
      `;

      // Create color segments
      this.updateSliderGradient(sliderTrack, thresholds);

      // Create thumbs with proper positioning
      const thumbData = [
        { type: 'green', color: '#00FF00', value: thresholds.green },
        { type: 'blue', color: '#0088FF', value: thresholds.blue },
        { type: 'orange', color: '#FFA500', value: thresholds.orange },
        { type: 'red', color: '#FF0000', value: thresholds.red }
      ];

      thumbData.forEach((thumb, index) => {
        const thumbElement = this.createThumb(thumb, indicatorName, index);
        sliderTrack.appendChild(thumbElement);
      });

      // Create value display
      const valueDisplay = document.createElement('div');
      valueDisplay.className = 'unified-value-display';
      valueDisplay.style.cssText = `
        display: flex;
        justify-content: space-between;
        margin-top: 15px;
        font-size: 11px;
        font-family: 'Roboto Mono', monospace;
      `;

      thumbData.forEach(thumb => {
        const valueLabel = document.createElement('span');
        valueLabel.className = `value-label-${thumb.type}`;
        valueLabel.style.cssText = `
          color: ${thumb.color};
          font-weight: bold;
          text-shadow: 0 0 3px ${thumb.color}40;
        `;
        valueLabel.textContent = `${thumb.type.toUpperCase()}: ${thumb.value}%`;
        valueDisplay.appendChild(valueLabel);
      });

      // Assemble the slider
      sliderContainer.appendChild(label);
      sliderContainer.appendChild(sliderTrack);
      sliderContainer.appendChild(valueDisplay);

      return sliderContainer;
    }

    // Create a draggable thumb
    createThumb(thumbData, indicatorName, zIndex) {
      const thumb = document.createElement('div');
      thumb.className = `unified-thumb unified-thumb-${thumbData.type}`;
      thumb.dataset.type = thumbData.type;
      thumb.dataset.indicator = indicatorName;
      thumb.style.cssText = `
        position: absolute;
        top: -8px;
        left: ${thumbData.value}%;
        width: 16px;
        height: 36px;
        background: ${thumbData.color};
        border: 2px solid #FFFFFF;
        border-radius: 8px;
        cursor: grab;
        transform: translateX(-50%);
        z-index: ${110 + zIndex};
        box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4), 0 0 10px ${thumbData.color}60;
        transition: all 0.2s ease;
      `;

      // Add hover effects
      thumb.addEventListener('mouseenter', () => {
        thumb.style.transform = 'translateX(-50%) scale(1.1)';
        thumb.style.boxShadow = `0 4px 12px rgba(0, 0, 0, 0.6), 0 0 15px ${thumbData.color}80`;
      });

      thumb.addEventListener('mouseleave', () => {
        if (!this.isDragging) {
          thumb.style.transform = 'translateX(-50%) scale(1)';
          thumb.style.boxShadow = `0 3px 8px rgba(0, 0, 0, 0.4), 0 0 10px ${thumbData.color}60`;
        }
      });

      // Add drag functionality
      thumb.addEventListener('mousedown', (e) => this.startDrag(e, thumb));

      return thumb;
    }

    // Update slider gradient based on threshold values
    updateSliderGradient(track, thresholds) {
      track.style.background = `linear-gradient(to right,
        #00FF00 0%, #00FF00 ${thresholds.green}%,
        #0088FF ${thresholds.green}%, #0088FF ${thresholds.blue}%,
        #808080 ${thresholds.blue}%, #808080 ${thresholds.orange}%,
        #FFA500 ${thresholds.orange}%, #FFA500 ${thresholds.red}%,
        #FF0000 ${thresholds.red}%, #FF0000 100%
      )`;
    }

    // Start dragging a thumb
    startDrag(e, thumb) {
      e.preventDefault();
      this.isDragging = true;
      this.activeThumb = thumb;
      this.startX = e.clientX;
      this.startValue = parseFloat(thumb.style.left) || 0;
      
      thumb.style.cursor = 'grabbing';
      thumb.style.transform = 'translateX(-50%) scale(1.2)';
      
      document.addEventListener('mousemove', this.handleDrag.bind(this));
      document.addEventListener('mouseup', this.stopDrag.bind(this));
    }

    // Handle thumb dragging
    handleDrag(e) {
      if (!this.isDragging || !this.activeThumb) return;

      const track = this.activeThumb.parentElement;
      const rect = track.getBoundingClientRect();
      const deltaX = e.clientX - this.startX;
      let newValue = this.startValue + (deltaX / rect.width) * 100;

      // Constrain to 0-100%
      newValue = Math.max(0, Math.min(100, newValue));

      // Apply ordering constraints
      const indicatorName = this.activeThumb.dataset.indicator;
      const type = this.activeThumb.dataset.type;
      const currentThresholds = window.thresholds[indicatorName];
      
      newValue = this.applyConstraints(newValue, type, currentThresholds);

      // Update thumb position
      this.activeThumb.style.left = `${newValue}%`;

      // Update threshold value
      window.thresholds[indicatorName][type] = Math.round(newValue);

      // Update visual elements
      this.updateSliderGradient(track, window.thresholds[indicatorName]);
      this.updateValueLabels(track.parentElement, indicatorName);

      // Update signal lights
      if (typeof updateAllSignalLights === 'function') {
        updateAllSignalLights();
      }
    }

    // Apply ordering constraints to thumb movement
    applyConstraints(newValue, type, thresholds) {
      switch (type) {
        case 'green':
          return Math.min(newValue, thresholds.blue - 1);
        case 'blue':
          return Math.max(Math.min(newValue, thresholds.orange - 1), thresholds.green + 1);
        case 'orange':
          return Math.max(Math.min(newValue, thresholds.red - 1), thresholds.blue + 1);
        case 'red':
          return Math.max(newValue, thresholds.orange + 1);
        default:
          return newValue;
      }
    }

    // Stop dragging
    stopDrag() {
      if (this.activeThumb) {
        this.activeThumb.style.cursor = 'grab';
        this.activeThumb.style.transform = 'translateX(-50%) scale(1)';
      }
      
      this.isDragging = false;
      this.activeThumb = null;
      
      document.removeEventListener('mousemove', this.handleDrag.bind(this));
      document.removeEventListener('mouseup', this.stopDrag.bind(this));
    }

    // Update value labels
    updateValueLabels(container, indicatorName) {
      const thresholds = window.thresholds[indicatorName];
      const valueDisplay = container.querySelector('.unified-value-display');
      
      if (valueDisplay && thresholds) {
        ['green', 'blue', 'orange', 'red'].forEach(type => {
          const label = valueDisplay.querySelector(`.value-label-${type}`);
          if (label) {
            label.textContent = `${type.toUpperCase()}: ${thresholds[type]}%`;
          }
        });
      }
    }

    // Render threshold sliders for current strategy
    renderThresholdSliders(strategy) {
      try {
        const container = document.getElementById('threshold-sliders');
        if (!container) {
          console.error('[UnifiedThresholds] Container not found');
          return;
        }

        // Clear existing content
        container.innerHTML = '';

        // Get strategy indicators
        const currentStrategy = strategy || window.currentStrategy || 'admiral_toa';
        const strategyIndicators = window.TRADING_STRATEGIES[currentStrategy]?.indicators || 
          Object.keys(window.thresholds || {}).filter(ind => ind !== 'atr');

        console.log(`[UnifiedThresholds] Rendering sliders for ${strategyIndicators.length} indicators`);

        // Create sliders for each indicator
        strategyIndicators.forEach(indicatorName => {
          if (window.thresholds[indicatorName] && indicatorName !== 'atr') {
            const slider = this.createUnifiedSlider(indicatorName, container);
            if (slider) {
              container.appendChild(slider);
            }
          }
        });

        // Add reset button
        this.addResetButton(container);

        console.log('[UnifiedThresholds] Threshold sliders rendered successfully');
      } catch (error) {
        console.error('[UnifiedThresholds] Error rendering sliders:', error);
      }
    }

    // Add reset button
    addResetButton(container) {
      const resetButton = document.createElement('button');
      resetButton.className = 'unified-reset-button';
      resetButton.textContent = '🔄 Reset to Defaults';
      resetButton.style.cssText = `
        display: block;
        margin: 20px auto;
        padding: 10px 20px;
        background: linear-gradient(135deg, rgba(0, 200, 255, 0.8), rgba(0, 100, 200, 0.8));
        color: white;
        border: none;
        border-radius: 6px;
        cursor: pointer;
        font-weight: bold;
        font-size: 12px;
        transition: all 0.3s ease;
        border: 1px solid rgba(0, 255, 255, 0.3);
      `;

      resetButton.addEventListener('click', () => {
        if (typeof resetThresholds === 'function') {
          resetThresholds();
          this.renderThresholdSliders();
        }
      });

      resetButton.addEventListener('mouseenter', () => {
        resetButton.style.background = 'linear-gradient(135deg, rgba(0, 255, 255, 0.9), rgba(0, 150, 255, 0.9))';
        resetButton.style.boxShadow = '0 0 15px rgba(0, 255, 255, 0.7)';
        resetButton.style.transform = 'translateY(-2px) scale(1.05)';
      });

      resetButton.addEventListener('mouseleave', () => {
        resetButton.style.background = 'linear-gradient(135deg, rgba(0, 200, 255, 0.8), rgba(0, 100, 200, 0.8))';
        resetButton.style.boxShadow = 'none';
        resetButton.style.transform = 'none';
      });

      container.appendChild(resetButton);
    }
  }

  // Initialize the unified threshold sliders
  window.unifiedThresholdSliders = new UnifiedThresholdSliders();

  // Replace the old renderThresholdSliders function
  window.renderThresholdSliders = function(strategy) {
    window.unifiedThresholdSliders.renderThresholdSliders(strategy);
  };

  console.log('[UnifiedThresholds] Unified threshold slider system initialized');

})();
